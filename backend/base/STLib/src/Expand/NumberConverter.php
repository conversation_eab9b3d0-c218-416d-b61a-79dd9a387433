<?php

namespace STLib\Expand;

trait NumberConverter {

    /**
     * 
     * @param string $stringValue
     * @return string
     */
    public function toNumber(?string $stringValue): ?string {
        if (is_null($stringValue) || empty($stringValue)) {
            return null;
        }
        return preg_replace([
            '/[^0-9,\.]/',
            '/,/'
        ], [
            '', 
            '.',
        ], $stringValue);
    }
    
    /**
     * 
     * @param string $string
     * @return int
     */
    public function stringToNumber(string $string): int {
        $result = '';
        foreach (str_split(strtoupper($string)) as $char) {
            if (is_numeric($char)) {
                $result .= $char;
            } else {
                $result .= ord($char) - 64;
            }
        }
        return (int) $result;
    }

}
