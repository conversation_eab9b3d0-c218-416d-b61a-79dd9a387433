<?php

declare(strict_types=1);

namespace tests\Unit\module\STCall\Service\Webhooks\Services;

use STCall\Service\Webhooks\Services\SummarizationWebhookService;
use tests\TestCase;

class SummarizationWebhookServiceTest extends TestCase
{
    public function testGetType(): void
    {
        $service = new SummarizationWebhookService();
        $this->assertSame('summarization', $service->getType());
    }

    /**
     * @return void
     */
    public function testFilterData(): void
    {
        $data = [
            'call_id' => '5222e604-4e7b-4e31-b993-6ead8581f7a3',
            'company_id' => 68,
            'overview' => 'Overview',
            'details' => 'Details'
        ];

        $filteredData = [
            'overview' => 'Overview',
            'details' => 'Details'
        ];

        $webhookService = new SummarizationWebhookService();

        $this->assertEquals($filteredData, $webhookService->filterData($data));
    }
}
