<?php

declare(strict_types=1);

namespace tests\Unit\module\STAlgo\Service;

use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\MockObject\Exception;
use STAlgo\Service\AiSolutionsCommutatorService;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\AlgoEvents\RequestCreation\ResponseToAlgoEventConverter;
use STAlgo\Service\Client;
use STAlgo\Service\Interfaces\TranslatorInterface;
use STCall\Entity\AlgoEventCollection;
use STCall\Entity\Call;
use STCall\Entity\CallCollection;
use STCall\Entity\ClientSummary;
use STCall\Entity\DTO\ClientSummaryDTO;
use STCall\Entity\Paragraph;
use STCall\Entity\ParagraphCollection;
use STCompany\Entity\Checklist\ChecklistPoint;
use STCompany\Entity\Checklist\ChecklistPointCollection;
use STCompany\Entity\Company;
use tests\TestCase;
use tests\WithConsecutive;

use function PHPUnit\Framework\assertNotEmpty;
use function PHPUnit\Framework\assertTrue;

class AiSolutionsCommutatorServiceTest extends TestCase
{
    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testGetAlgoEventsFromCall(): void
    {
        $company = $this->createMock(Company::class);
        $call = $this->createMock(Call::class);

        $translator = $this->createMock(TranslatorInterface::class);

        $request1 = $this->createMock(EventsAlgoApiRequestInterface::class);
        $request2 = $this->createMock(EventsAlgoApiRequestInterface::class);

        $result1 = ['some result key 1' => 'some result value 1'];
        $result2 = ['some result key 1' => 'some result value 1'];

        $jsonResult1 = json_encode($result1);
        $jsonResult2 = json_encode($result2);
        $clientAnalyzeMap = [
            [$request1, $jsonResult1],
            [$request2, $jsonResult2],
        ];
        $client = $this->createMock(Client::class);
        $client
            ->method('analyze')
            ->willReturnMap($clientAnalyzeMap);

        $algoEventCollection = new AlgoEventCollection();
        $responseToAlgoEventConverter = $this->createMock(ResponseToAlgoEventConverter::class);
        $responseToAlgoEventConverter
            ->expects($this->exactly(2))
            ->method('convert')
            ->with(
                ...
                WithConsecutive::create(
                    [$algoEventCollection, $jsonResult1, $company, $call, $request1],
                    [$algoEventCollection, $jsonResult2, $company, $call, $request2],
                )
            );

        $commutator = new AiSolutionsCommutatorService($client, $translator, $responseToAlgoEventConverter);
        $commutator->getAlgoEventsFromCall($company, $call, [$request1, $request2]);
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     */
    public function testChecklistResult(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);

        $callId = $this->faker->text();
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);

        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn(0);
        $paragraph1->method('getText')->willReturn($this->faker->text(100));
        $paragraph1->method('getSpeakerNumber')->willReturn(0);
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn(0);
        $paragraph2->method('getText')->willReturn($this->faker->text(100));
        $paragraph2->method('getSpeakerNumber')->willReturn(0);
        $paragraphsCollection = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);

        $checklistPoint = $this->createMock(ChecklistPoint::class);
        $checklistPoint->method('getTitle')->willReturn('Re-engaging the Lead');
        $checklistPoint->method('getDescription')->willReturn(
            'Explain the reason for contacting the lead '
        );
        $checklistPoint->method('getExpectedActions')->willReturn(
            'Explain the reason for the call and ask questions to understand the lead’s interest'
        );
        $checklistPoint->method('getGoodPerformanceDescription')->willReturn(
            'Clearly explains the reason for contact and asks open-ended questions about the material'
        );
        $checklistPoint->method('getGoodPerformanceExample')->willReturn(
            'I call to tell about our new sales'
        );
        $checklistPoint->method('getBadPerformanceDescription')->willReturn(
            'Does not clarify the reason for the call or fails to explore the lead’s interest'
        );
        $checklistPoint->method('getBadPerformanceExample')->willReturn(
            'Hello, I want to speak with you'
        );
        $checklistPointsCollections = new ChecklistPointCollection([
            $checklistPoint->getId() => $checklistPoint,
        ]);

        $translator = $this->createMock(TranslatorInterface::class);
        $client = $this->createMock(Client::class);
        $client
            ->method('getChecklistResult')
            ->willReturn([
                'status' => 'ok',
                'results' =>  [
                    'checklist_result' => [
                        [
                            'title' => 'Re-engaging the Lead',
                            'decision' => 'Not a Positive Event',
                            'cot' => 'The call transcript does not provide any context about the reason for the call',
                        ],
                    ],
                    'company_id' => $companyId,
                    'call_id' => $call->getId()
                ]
            ]);

        $commutator = new AiSolutionsCommutatorService(
            $client,
            $translator,
            $this->createMock(ResponseToAlgoEventConverter::class)
        );
        $checklistResult = $commutator->getChecklistResult($call, $paragraphsCollection, $checklistPointsCollections);

        assertTrue($checklistResult->status === 'ok');
        assertNotEmpty($checklistResult->getChecklistResult());
    }

    /**
     * @throws Exception
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function testGetParagraphsSpeakersRolesEnTextFallback(): void
    {
        $companyId = $this->faker->numberBetween(1, 100);
        $callId = $this->faker->text();
        
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getCompanyId')->willReturn($companyId);
        $call->method('getLanguage')->willReturn(null);
        
        // Create paragraph with both text and enText
        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn(1);
        $originalText1 = $this->faker->text(100);
        $englishText1 = $this->faker->text(100);
        $paragraph1->method('getText')->willReturn($originalText1);
        $paragraph1->method('getEnText')->willReturn($englishText1);
        $paragraph1->method('getSpeakerNumber')->willReturn(1);
        
        // Create paragraph with only text (enText is null)
        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn(2);
        $originalText2 = $this->faker->text(100);
        $paragraph2->method('getText')->willReturn($originalText2);
        $paragraph2->method('getEnText')->willReturn(null);
        $paragraph2->method('getSpeakerNumber')->willReturn(2);
        
        $paragraphsCollection = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);
        
        // Expected request parameters
        $expectedRequestParams = [
            'paragraphs' => [
                [
                    'id' => 1,
                    'text' => $originalText1,
                    'en_text' => $englishText1,
                    'speaker_number' => 1,
                ],
                [
                    'id' => 2,
                    'text' => $originalText2,
                    'en_text' => $originalText2,
                    'speaker_number' => 2,
                ],
            ],
            'company_id' => (string) $companyId,
            'call_id' => $callId,
        ];

        $speakersResult = [
            'status' => 'ok',
            'results' => [
                'speakers' => [
                    ['speaker_number' => 1, 'role' => 'agent'],
                    ['speaker_number' => 2, 'role' => 'client'],
                ]
            ]
        ];
        
        $translator = $this->createMock(TranslatorInterface::class);
        $client = $this->createMock(Client::class);
        $client
            ->expects($this->once())
            ->method('getSpeakers')
            ->with($expectedRequestParams)
            ->willReturn(json_encode($speakersResult, JSON_THROW_ON_ERROR));
        
        $commutator = new AiSolutionsCommutatorService(
            $client,
            $translator,
            $this->createMock(ResponseToAlgoEventConverter::class)
        );
        
        $result = $commutator->getParagraphsSpeakersRoles($call, $paragraphsCollection);
        
        $this->assertEquals('ok', $result->status);
        $this->assertEquals($speakersResult['results'], $result->results);
    }

    /**
     * @throws Exception
     */
    public function testGetClientSummarySuccessWithoutPreviousSummary(): void
    {
        // Arrange
        $companyId = 123;
        $callId1 = 'call-1';
        $callId2 = 'call-2';

        // Mock paragraphs
        $paragraph1 = $this->createMock(Paragraph::class);
        $paragraph1->method('getParagraphNumber')->willReturn(1);
        $paragraph1->method('getEnText')->willReturn('English text 1');
        $paragraph1->method('getText')->willReturn('Original text 1');

        $paragraph2 = $this->createMock(Paragraph::class);
        $paragraph2->method('getParagraphNumber')->willReturn(2);
        $paragraph2->method('getEnText')->willReturn(null);
        $paragraph2->method('getText')->willReturn('Original text 2');

        $paragraph3 = $this->createMock(Paragraph::class);
        $paragraph3->method('getParagraphNumber')->willReturn(1);
        $paragraph3->method('getEnText')->willReturn('English text 3');
        $paragraph3->method('getText')->willReturn('Original text 3');

        // Mock paragraph collections
        $paragraphCollection1 = new ParagraphCollection([
            $paragraph1->getParagraphNumber() => $paragraph1,
            $paragraph2->getParagraphNumber() => $paragraph2,
        ]);
        $paragraphCollection2 = new ParagraphCollection([
            $paragraph3->getParagraphNumber() => $paragraph3,
        ]);

        // Mock calls
        $call1 = $this->createMock(Call::class);
        $call1->method('getId')->willReturn($callId1);
        $call1->method('getTime')->willReturn(\Carbon\Carbon::parse('2024-01-01 10:00:00'));
        $call1->method('getParagraphs')->willReturn($paragraphCollection1);

        $call2 = $this->createMock(Call::class);
        $call2->method('getId')->willReturn($callId2);
        $call2->method('getTime')->willReturn(\Carbon\Carbon::parse('2024-01-01 11:00:00'));
        $call2->method('getParagraphs')->willReturn($paragraphCollection2);

        // Mock call collection
        $callCollection = new CallCollection(
            [
                $call1->getId() => $call1,
                $call2->getId() => $call2,
            ]
        );

        // Expected request params
        $expectedParams = [
            'company_id' => $companyId,
            'calls' => [
                [
                    'call_id' => $callId1,
                    'call_time' => '2024-01-01 10:00:00',
                    'paragraphs' => [
                        ['id' => 1, 'en_text' => 'English text 1'],
                        ['id' => 2, 'en_text' => 'Original text 2']
                    ]
                ],
                [
                    'call_id' => $callId2,
                    'call_time' => '2024-01-01 11:00:00',
                    'paragraphs' => [
                        ['id' => 1, 'en_text' => 'English text 3']
                    ]
                ]
            ]
        ];

        // Mock API response
        $apiResponse = [
            'status' => 'ok',
            'results' => [
                'summaries' => [
                    [
                        'primary_purpose' => 'Sales call',
                        'deal_size' => '$10,000',
                        'timeline' => ['Q1 2024'],
                        'deal_status' => 'In progress',
                        'next_step' => 'Follow up',
                        'customer_sentiment' => 'Positive',
                        'client_discovery_parameters' => ['Budget confirmed'],
                        'customer_problems' => ['Need solution'],
                        'business_opportunities' => ['Upsell potential'],
                        'risks' => 'Low risk',
                        'agent_performance' => 'Good',
                        'interaction_notes' => ['Important call']
                    ]
                ]
            ]
        ];

        $client = $this->createMock(Client::class);
        $client
            ->expects($this->once())
            ->method('getClientSummary')
            ->with($expectedParams)
            ->willReturn($apiResponse);

        $translator = $this->createMock(TranslatorInterface::class);
        $responseConverter = $this->createMock(ResponseToAlgoEventConverter::class);

        $service = new AiSolutionsCommutatorService($client, $translator, $responseConverter);

        // Act
        $result = $service->getClientSummary($companyId, $callCollection);

        // Assert
        $this->assertInstanceOf(ClientSummaryDTO::class, $result);
        $this->assertEquals('Sales call', $result->primaryPurpose);
        $this->assertEquals('$10,000', $result->dealSize);
        $this->assertEquals(['Q1 2024'], $result->timeline);
        $this->assertEquals('In progress', $result->dealStatus);
        $this->assertEquals('Follow up', $result->nextStep);
        $this->assertEquals('Positive', $result->customerSentiment);
        $this->assertEquals(['Budget confirmed'], $result->clientDiscoveryParameters);
        $this->assertEquals(['Need solution'], $result->customerProblems);
        $this->assertEquals(['Upsell potential'], $result->businessOpportunities);
        $this->assertEquals('Low risk', $result->risks);
        $this->assertEquals('Good', $result->agentPerformance);
        $this->assertEquals(['Important call'], $result->interactionNotes);
    }

    /**
     * @throws Exception
     */
    public function testGetClientSummarySuccessWithPreviousSummary(): void
    {
        // Arrange
        $companyId = 456;
        $callId = 'call-1';

        // Mock paragraph
        $paragraph = $this->createMock(Paragraph::class);
        $paragraph->method('getParagraphNumber')->willReturn(1);
        $paragraph->method('getEnText')->willReturn('New call text');
        $paragraph->method('getText')->willReturn('New call text');

        // Mock paragraph collection
        $paragraphCollection = new ParagraphCollection([
            $paragraph->getParagraphNumber() => $paragraph,
        ]);

        // Mock call
        $call = $this->createMock(Call::class);
        $call->method('getId')->willReturn($callId);
        $call->method('getTime')->willReturn(\Carbon\Carbon::parse('2024-01-02 10:00:00'));
        $call->method('getParagraphs')->willReturn($paragraphCollection);

        // Mock call collection
        $callCollection = new CallCollection(
            [
                $call->getId() => $call,
            ]
        );
        // Mock previous summary
        $previousSummary = $this->createMock(ClientSummary::class);
        $previousSummary->method('getPrimaryPurpose')->willReturn('Previous purpose');
        $previousSummary->method('getDealSize')->willReturn('$5,000');
        $previousSummary->method('getTimeline')->willReturn(['Q4 2023']);
        $previousSummary->method('getDealStatus')->willReturn('Negotiating');
        $previousSummary->method('getNextStep')->willReturn('Send proposal');
        $previousSummary->method('getCustomerSentiment')->willReturn('Neutral');
        $previousSummary->method('getClientDiscoveryParameters')->willReturn(['Budget discussed']);
        $previousSummary->method('getCustomerProblems')->willReturn(['Current solution inadequate']);
        $previousSummary->method('getBusinessOpportunities')->willReturn(['Cross-sell opportunity']);
        $previousSummary->method('getRisks')->willReturn('Medium risk');
        $previousSummary->method('getAgentPerformance')->willReturn('Average');
        $previousSummary->method('getInteractionNotes')->willReturn(['Previous notes']);

        // Expected request params with previous summary
        $expectedParams = [
            'company_id' => $companyId,
            'calls' => [
                [
                    'call_id' => $callId,
                    'call_time' => '2024-01-02 10:00:00',
                    'paragraphs' => [
                        ['id' => 1, 'en_text' => 'New call text']
                    ]
                ]
            ],
            'previous_summary' => [
                'primary_purpose' => 'Previous purpose',
                'deal_size' => '$5,000',
                'timeline' => ['Q4 2023'],
                'deal_status' => 'Negotiating',
                'next_step' => 'Send proposal',
                'customer_sentiment' => 'Neutral',
                'client_discovery_parameters' => ['Budget discussed'],
                'customer_problems' => ['Current solution inadequate'],
                'business_opportunities' => ['Cross-sell opportunity'],
                'risks' => 'Medium risk',
                'agent_performance' => 'Average',
                'interaction_notes' => ['Previous notes']
            ]
        ];

        // Mock API response
        $apiResponse = [
            'status' => 'ok',
            'results' => [
                'summaries' => [
                    [
                        'primary_purpose' => 'Updated purpose',
                        'deal_size' => '$15,000',
                        'timeline' => ['Q1 2024', 'Q2 2024'],
                        'deal_status' => 'Closing',
                        'next_step' => 'Final approval',
                        'customer_sentiment' => 'Very positive',
                        'client_discovery_parameters' => ['Budget confirmed', 'Decision maker identified'],
                        'customer_problems' => ['Scalability issues'],
                        'business_opportunities' => ['Enterprise upgrade'],
                        'risks' => 'Low risk',
                        'agent_performance' => 'Excellent',
                        'interaction_notes' => ['Ready to close']
                    ]
                ]
            ]
        ];

        $client = $this->createMock(Client::class);
        $client
            ->expects($this->once())
            ->method('getClientSummary')
            ->with($expectedParams)
            ->willReturn($apiResponse);

        $translator = $this->createMock(TranslatorInterface::class);
        $responseConverter = $this->createMock(ResponseToAlgoEventConverter::class);

        $service = new AiSolutionsCommutatorService($client, $translator, $responseConverter);

        // Act
        $result = $service->getClientSummary($companyId, $callCollection, $previousSummary);

        // Assert
        $this->assertInstanceOf(ClientSummaryDTO::class, $result);
        $this->assertEquals('Updated purpose', $result->primaryPurpose);
        $this->assertEquals('$15,000', $result->dealSize);
        $this->assertEquals(['Q1 2024', 'Q2 2024'], $result->timeline);
        $this->assertEquals('Closing', $result->dealStatus);
        $this->assertEquals('Final approval', $result->nextStep);
        $this->assertEquals('Very positive', $result->customerSentiment);
        $this->assertEquals(['Budget confirmed', 'Decision maker identified'], $result->clientDiscoveryParameters);
        $this->assertEquals(['Scalability issues'], $result->customerProblems);
        $this->assertEquals(['Enterprise upgrade'], $result->businessOpportunities);
        $this->assertEquals('Low risk', $result->risks);
        $this->assertEquals('Excellent', $result->agentPerformance);
        $this->assertEquals(['Ready to close'], $result->interactionNotes);
    }
}
