<?php

namespace tests\Unit\module\STRoboTruck\Service\Logger;

use Carbon\Carbon;
use Exception;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STRabbit\Service\RabbitService;
use STRoboTruck\Service\Logger\Writer;
use STRoboTruck\Service\PusherService;
use tests\TestCase;

class WriterTest extends TestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testWrite()
    {
        $now = Carbon::now();
        Carbon::setTestNow($now);

        $message = $this->faker->text(20);
        $source = $this->faker->word();
        $eventName = $this->faker->word();
        $data = [
            'some_key 1' => 'some value 1',
            'some_key 2' => 'some value 2',
        ];
        $extra = $data;
        $extra['source'] = $source;
        $extra['event_name'] = $eventName;

        $event = [
            'message' => $message,
            'extra' => $extra,
        ];

        $env = $this->faker->randomElement(['local', 'stage', 'production']);
        $expectedAMQPMessageBody = [
            'events' => [
                [
                    'env' => $env,
                    'name' => $eventName,
                    'time' => $now->format('Y-m-d H:i:s.v'),
                    'source' => $source,
                    'properties' => array_merge($data, ['message' => $message]),
                ]
            ]
        ];
        $channel = $this->createMock(AMQPChannel::class);
        $channel
            ->expects($this->once())
            ->method('basic_publish')
            ->with(
                self::callback(
                    function (AMQPMessage $message) use ($expectedAMQPMessageBody) {
                        $messageBody = json_decode($message->getBody(), true);

                        return $expectedAMQPMessageBody === $messageBody;
                    }
                ),
                '',
                PusherService::ROBO_TRUCK_QUEUE_NAME
            );

        $rabbit = $this->createMock(RabbitService::class);
        $rabbit
            ->method('getChannel')
            ->willReturn($channel);

        Writer::setRabbit($rabbit);
        $writer = new Writer();
        $writer->setSettings(['env' => $env]);
        $writer->write($event);
    }
}
