<?php

declare(strict_types=1);

namespace tests\Feature\Companies\Webhooks;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use Psr\Container\ContainerExceptionInterface;
use STApi\Entity\Exception\NotFoundApiException;
use STCompany\Data\CompaniesWebhooksSettingsTable;
use STCompany\Data\PermissionsTable;
use STCompany\Entity\Role;
use STCompany\Service\Webhooks\WebhookSettingsSelector;
use tests\Feature\AuthTestCase;

final class DeleteWebhookSettingsTest extends AuthTestCase
{
    /**
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     * @throws ContainerExceptionInterface
     */
    public function testDelete(): void
    {
        $webhookId = $this->faker->numberBetween(1, 100);

        $webhookSettingsData = ['id' => $webhookId];
        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('deleteWebhookSettings')
            ->with($webhookId);

        $webhookSettingsTable
            ->expects($this->once())
            ->method('getWebhookSettingsData')
            ->with($webhookId, $this->companyId)
            ->willReturn($webhookSettingsData);
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::WRITE_PERMISSION
        );

        $this->dispatchApi('company/webhook/' . $webhookId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $expectedMessage = 'Webhook ' . $webhookId . ' has been successfully deleted';
        $this->assertTrue($response['result']['is_deleted']);
        $this->assertSame($expectedMessage, $response['result']['message']);

        $this->assertResponseStatusCode(200);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenWebhookDoesntExist(): void
    {
        $webhookId = $this->faker->numberBetween(1, 100);

        $webhookSettingsTable = $this->createMock(CompaniesWebhooksSettingsTable::class);
        $webhookSettingsTable
            ->expects($this->once())
            ->method('getWebhookSettingsData')
            ->with($webhookId, $this->companyId)
            ->willThrowException(new NotFoundApiException());
        $this->serviceManager->setService(CompaniesWebhooksSettingsTable::class, $webhookSettingsTable);

        $this->serviceManager->setService(
            WebhookSettingsSelector::class,
            $this->serviceManager->build(WebhookSettingsSelector::class)
        );

        $this->loginAs(Role::ADMIN_ROLE_TYPE);
        $this->dispatchApi('company/webhook/' . $webhookId, 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame('The webhook does not exists.', $response['error']['messages']['id'][0]);

        $this->assertResponseStatusCode(422);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenReadPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->addPermissionsToCompanyManager(
            PermissionsTable::WEBHOOKS,
            PermissionsTable::READ_PERMISSION
        );

        $this->dispatchApi('company/webhook/' . $this->faker->numberBetween(1, 100), 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User has required permission, but doesn\'t have adequate access level',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testDeleteWhenNoPermissions(): void
    {
        $this->loginAs(Role::MANAGER_ROLE_TYPE);
        $this->dispatchApi('company/webhook/' . $this->faker->numberBetween(1, 100), 'DELETE');

        $response = json_decode($this->getResponse()->getContent(), true);

        $this->assertSame(
            'User doesn\'t have required permission',
            $response['error']['messages']
        );

        $this->assertResponseStatusCode(403);
    }
}
