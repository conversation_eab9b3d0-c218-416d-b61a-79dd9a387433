<?php

declare(strict_types=1);

namespace tests\Feature\Onboarding;

use Exception;
use PHPUnit\Framework\MockObject\Exception as PHPUnitException;
use STApi\Entity\Exception\NotFoundApiException;
use STCall\Data\CallsTable;
use STOnboarding\Data\OnboardingFormsTable;
use STOnboarding\Entity\OnboardingForm;
use tests\Feature\AppTokenTestCase;

final class GetFormTest extends AppTokenTestCase
{
    /**
     * @dataProvider companyNameDataProvider
     * @param string|null $companyName
     * @return void
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetForm(?string $companyName): void
    {
        $onboardingFormId = $this->faker->numberBetween(1, 100);
        $externalId = $this->faker->uuid();
        $frontFormLink = $this->faker->url();
        $inviteLink = $this->faker->url();
        $companyLogo = $this->faker->text(1000);
        $isSubmitted = $this->faker->boolean();
        $companyId = $this->faker->numberBetween(101, 200);

        $users = [
            [
                'name' => $this->faker->firstName() . ' ' . $this->faker->lastName(),
                'email' => $this->faker->email(),
            ],
            [
                'name' => $this->faker->firstName() . ' ' . $this->faker->lastName(),
                'email' => $this->faker->email(),
            ],
        ];

        $languageCodes = array_keys(CallsTable::LANGUAGES);
        $callsSettings = [
            'languages' => [
                $this->faker->randomElement($languageCodes),
                $this->faker->randomElement($languageCodes),
                $this->faker->randomElement($languageCodes),
            ],
            'min_call_duration_for_auto_analyze' => $this->faker->numberBetween(1, 300),
        ];

        $existingIndustryId = $this->faker->numberBetween(201, 300);
        $newIndustryName = $this->faker->word();
        $industries = [
            $existingIndustryId => [
                'id' => $existingIndustryId,
                'is_active' => false,
                'type' => 'existing',
                'events' => [
                    [
                        'id' => $this->faker->numberBetween(301, 400),
                    ],
                    [
                        'name' => $this->faker->word(),
                        'description' => $this->faker->sentence(),
                    ]
                ]
            ],
            $newIndustryName => [
                'name' => $newIndustryName,
                'is_active' => true,
                'type' => 'new',
                'events' => [
                    [
                        'name' => $this->faker->sentence(2),
                        'description' => $this->faker->text(),
                    ],
                    [
                        'id' => $this->faker->numberBetween(401, 500),
                    ]
                ]
            ]
        ];

        $onboardingForm = new OnboardingForm();
        $onboardingForm->setId($onboardingFormId);
        $onboardingForm->setCompanyName($companyName);
        $onboardingForm->setExternalId($externalId);
        $onboardingForm->setFrontFormLink($frontFormLink);
        $onboardingForm->setInviteLink($inviteLink);
        $onboardingForm->setCompanyLogo($companyLogo);
        $onboardingForm->setUsers($users);
        $onboardingForm->setIndustries($industries);
        $onboardingForm->setCallsSettings($callsSettings);
        $onboardingForm->setIsSubmitted($isSubmitted);
        $onboardingForm->setCompanyId($companyId);

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willReturn($onboardingForm);

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId, 'GET');

        $expectedData = json_encode([
            'result' => [
                'form' => [
                    'id' => $externalId,
                    'front_form_link' => $frontFormLink,
                    'invite_link' => $inviteLink,
                    'company_name' => $companyName,
                    'company_logo' => $companyLogo,
                    'users' => $users,
                    'industries' => $industries,
                    'calls_settings' => $callsSettings,
                    'is_submitted' => $isSubmitted,
                    'company_id' => $companyId
                ],
            ],
            'error' => null,
        ]);

        $this->assertSame($expectedData, $this->getResponse()->getContent());

        $this->assertResponseStatusCode(200);
    }

    public static function companyNameDataProvider(): array
    {
        return [
            ['some company name'],
            [null],
        ];
    }

    /**
     * @throws PHPUnitException
     * @throws Exception
     */
    public function testGetFormWhenWrongId(): void
    {
        $externalId = $this->faker->uuid();

        $onboardingFormsTable = $this->createMock(OnboardingFormsTable::class);
        $onboardingFormsTable
            ->method('getFormByExternalId')
            ->with($externalId)
            ->willThrowException(new NotFoundApiException());

        $this->serviceManager->setService(OnboardingFormsTable::class, $onboardingFormsTable);

        $this->dispatchApi('onboarding/form/' . $externalId, 'GET');

        $expectedError = '"id":["The onboarding form does not exists."]';

        $this->assertStringContainsString($expectedError, $this->getResponse()->getContent());
        $this->assertResponseStatusCode(422);
    }
}
