<?php

declare(strict_types=1);

namespace Api;

use Api\Controller\V0\CallSummarizationController;
use Api\Controller\V0\WebhookController;
use Laminas\Router\Http\Method;
use Laminas\Router\Http\Segment;
use STCompany\Data\PermissionsTable;

return [
    'router' => [
        'routes' => [
            'api' => [
                'type' => Segment::class,
                'options' => [
                    'route' => '/api[/]',
                    'defaults' => [
                        'controller' => Controller\V0\IndexController::class,
                        'action' => 'not-found',
                    ],
                ],
                'child_routes' => [
//                    'not-found' => [
//                        'type' => \Laminas\Router\Http\Regex::class,
//                        'options' => [
//                            'regex' => '.*',
//                            'spec' => '',
//                        ],
//                    ],
                    'v0' => [
                        'type' => Segment::class,
                        'options' => [
                            'route' => 'v0[/]',
                            'defaults' => [
                                'access-checks' => [
                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                    Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                ],
                            ],
                        ],
                        'child_routes' => [
                            'pdf' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'pdf[/]',
                                ],
                                'child_routes' => [
                                    'post' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'pdf',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'knowledge-base-proxy' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'knowledge-base-proxy[/[:path]]',
                                    'constraints' => [
                                        'path' => '.*',
                                    ],
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'knowledge-base-proxy',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'languages' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'languages[/]',
                                ],
                                'child_routes' => [
                                    'post' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-languages',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'front' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'front[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\FrontController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-front',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'robo-truck' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'robo-truck[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\RoboTruckController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'post' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'add-log',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'auth' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'auth[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\AuthController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'sign-up' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'sign-up',
                                            ],
                                        ],
                                    ],
                                    'sign-in' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'put',
                                            'defaults' => [
                                                'action' => 'sign-in',
                                            ],
                                        ],
                                    ],
                                    'sign-out' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'delete',
                                            'defaults' => [
                                                'action' => 'sign-out',
                                            ],
                                        ],
                                    ],
                                    'password-recovery' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'password-recovery[/]',
                                        ],
                                        'child_routes' => [
                                            'request' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'password-recovery-request',
                                                    ],
                                                ],
                                            ],
                                            'update-password' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'password-recovery-update',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'algo-apis' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'algo-apis[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\AlgoApiController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'compare' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'compare[/]',
                                        ],
                                        'child_routes' => [
                                            'compare-algo-apis' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'compare-algo-apis',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'get-algo-apis' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-algo-apis',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'algo-api' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'algo-api[/]',
                                ],
                                'child_routes' => [
                                    'algo-api' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':algo_api_id[/]',
                                        ],
                                        'child_routes' => [
                                            'industries' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'industries[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\AlgoApiIndustryController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-industries' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-industries',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'industry' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'industry[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\AlgoApiIndustryController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'industry' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':industry_id[/]',
                                                            'constraints' => [
                                                                'id' => '[\d]*',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'connect-industry' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'post',
                                                                    'defaults' => [
                                                                        'action' => 'connect-industry',
                                                                    ],
                                                                ],
                                                            ],
                                                            'disconnect-industry' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'delete',
                                                                    'defaults' => [
                                                                        'action' => 'disconnect-industry',
                                                                    ],
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'ems' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'ems[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\EmsController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                        ],
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::EMS,
                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'data-set-example' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'data-set-example[/]',
                                        ],
                                        'child_routes' => [
                                            'add-data-set-example' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save-data-set-example',
                                                    ],
                                                ],
                                            ],
                                            'data-set-example' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':data_set_example_id[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-data-set-example' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-data-set-example',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EMS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'update-data-set-example' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save-data-set-example',
                                                            ],
                                                        ],
                                                    ],
                                                    'restore-data-set-example' => [
                                                        'type' => \Laminas\Router\Http\Segment::class,
                                                        'options' => [
                                                            'route' => 'restore[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'restore-data-set-example' => [
                                                                'type' => \Laminas\Router\Http\Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'restore-data-set-example',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-data-set-example' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-data-set-example',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'data-set-example-event' => [
                                        'type' => \Laminas\Router\Http\Segment::class,
                                        'options' => [
                                            'route' => 'data-set-example-event[/]',
                                        ],
                                        'child_routes' => [
                                            'add-data-set-example-event' => [
                                                'type' => \Laminas\Router\Http\Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save-data-set-example-event',
                                                    ],
                                                ],
                                            ],
                                            'data-set-example-event' => [
                                                'type' => \Laminas\Router\Http\Segment::class,
                                                'options' => [
                                                    'route' => ':data_set_example_event_id[/]',
                                                ],
                                                'child_routes' => [
                                                    'update-data-set-example-event' => [
                                                        'type' => \Laminas\Router\Http\Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save-data-set-example-event',
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-data-set-example-event' => [
                                                        'type' => \Laminas\Router\Http\Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-data-set-example-event',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'event' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'event[/]',
                                        ],
                                        'child_routes' => [
                                            'event' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':event_id[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-data-set-by-event-id' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'route' => 'data-set[/]',
                                                            'defaults' => [
                                                                'action' => 'get-data-set-by-event-id',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'data-set' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'data-set[/]',
                                        ],
                                        'child_routes' => [
                                            'create-data-set' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'create-data-set',
                                                    ],
                                                ],
                                            ],
                                            'data-set' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':data_set_id[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-data-set' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-data-set',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EMS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'get-data-set-examples' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'route' => 'examples[/]',
                                                            'defaults' => [
                                                                'action' => 'get-data-set-examples',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EMS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'get-analyzed-calls-events' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'route' => 'analyzed-calls-events[/]',
                                                            'defaults' => [
                                                                'action' => 'get-analyzed-calls-events',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EMS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'update-data-set' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'update-data-set',
                                                            ],
                                                        ],
                                                    ],
                                                    'restore-data-set-examples' => [
                                                        'type' => \Laminas\Router\Http\Segment::class,
                                                        'options' => [
                                                            'route' => 'restore[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'restore-data-set-examples' => [
                                                                'type' => \Laminas\Router\Http\Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'restore-data-set-examples',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'confirm-data-set' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'route' => 'confirm[/]',
                                                            'defaults' => [
                                                                'action' => 'confirm-data-set',
                                                            ],
                                                        ],
                                                    ],
                                                    'run-reviewed-calls-examples-search' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'route' => 'run-reviewed-calls-examples-search[/]',
                                                            'defaults' => [
                                                                'action' => 'run-reviewed-calls-examples-search',
                                                            ],
                                                        ],
                                                    ],
                                                    'run-analyzed-calls-examples-search' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'route' => 'run-analyzed-calls-examples-search[/]',
                                                            'defaults' => [
                                                                'action' => 'run-analyzed-calls-examples-search',
                                                            ],
                                                        ],
                                                    ],
                                                    'reopen-data-set' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'route' => 'reopen[/]',
                                                            'defaults' => [
                                                                'action' => 'create-data-set',
                                                            ],
                                                        ],
                                                    ],
                                                    'upload-example' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'route' => 'upload-examples[/]',
                                                            'defaults' => [
                                                                'action' => 'upload-data-set-examples',
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-data-set' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-data-set',
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-data-set-examples' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'route' => 'delete-examples[/]',
                                                            'defaults' => [
                                                                'action' => 'delete-data-set-examples',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'dashboard' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'dashboard[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\DashboardController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'calls-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'calls-statistics[/]',
                                            'defaults' => [
                                                'action' => 'calls-statistics',
                                            ],
                                        ],
                                    ],
                                    'managers-kpi' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'managers-kpi[/]',
                                            'defaults' => [
                                                'action' => 'managers-kpi',
                                            ],
                                        ],
                                    ],
                                    'managers-kpi-csv' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'managers-kpi-csv[/]',
                                            'defaults' => [
                                                'action' => 'managers-kpi-csv',
                                            ],
                                        ],
                                    ],
                                    'detailed-managers-kpi' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'detailed-managers-kpi[/]',
                                            'defaults' => [
                                                'action' => 'detailed-managers-kpi',
                                            ],
                                        ],
                                    ],
                                    'reviewed-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'reviewed-statistics[/]',
                                            'defaults' => [
                                                'action' => 'reviewed-statistics',
                                            ],
                                        ],
                                    ],
                                    'detailed-reviewed-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'detailed-reviewed-statistics[/]',
                                            'defaults' => [
                                                'action' => 'detailed-reviewed-statistics',
                                            ],
                                        ],
                                    ],
                                    'reviewed-events-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'reviewed-events-statistics[/]',
                                            'defaults' => [
                                                'action' => 'reviewed-events-statistics',
                                            ],
                                        ],
                                    ],
                                    'events-categories-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'events-categories-statistics[/]',
                                            'defaults' => [
                                                'action' => 'events-categories-statistics',
                                            ],
                                        ],
                                    ],
                                    'detailed-events-categories-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'detailed-events-categories-statistics[/]',
                                            'defaults' => [
                                                'action' => 'detailed-events-categories-statistics',
                                            ],
                                        ],
                                    ],
                                    'reviewed-clients-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'reviewed-clients-statistics[/]',
                                            'defaults' => [
                                                'action' => 'reviewed-clients-statistics',
                                            ],
                                        ],
                                    ],
                                    'calls-languages-statistics' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'route' => 'calls-languages-statistics[/]',
                                            'defaults' => [
                                                'action' => 'calls-languages-statistics',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'permissions' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'permissions[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\PermissionController::class,
                                    ],
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-permissions',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'user' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'user[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\UserController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get-user' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-user',
                                            ],
                                        ],
                                    ],
                                    'edit-user' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'put',
                                            'defaults' => [
                                                'action' => 'save-user',
                                            ],
                                        ],
                                    ],
                                    'password' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'password[/]',
                                        ],
                                        'child_routes' => [
                                            'update' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'change-password',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'companies' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'companies[/]',
                                        ],
                                        'child_routes' => [
                                            'get-companies' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-user-companies',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'permissions' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'permissions[/]',
                                        ],
                                        'child_routes' => [
                                            'get-permissions' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-user-permissions',
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'company' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'company[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\CompanyController::class,
                                    ],
                                ],
                                'child_routes' => [
                                    'get-company' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-company',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                ],
                                            ],
                                        ],
                                    ],
                                    'create-company' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'save-company',
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                ],
                                            ],
                                        ],
                                    ],
                                    'edit-company' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'put',
                                            'defaults' => [
                                                'action' => 'save-company',
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::COMPANY,
                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                    ],
                                    'events' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'events[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\EventController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::EVENTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-events' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-events',
                                                    ],
                                                ],
                                            ],
                                            'algo-events' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'algo-events[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-algo-events' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-algo-events',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'algo-apis-events' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'algo-apis-events[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-algo-apis-events' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-algo-apis-events',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'categories' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'categories[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\EventCategoryController::class,
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-categories' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-categories',
                                                            ],
                                                        ],
                                                    ],
                                                    'colors' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'colors[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'get-colors' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'get',
                                                                    'defaults' => [
                                                                        'action' => 'get-event-colors',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'category' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'category[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\EventCategoryController::class,
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'category' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':category_id[/]',
                                                            'constraints' => [
                                                                'category_id' => '[\d]*',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'get-category' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'get',
                                                                    'defaults' => [
                                                                        'action' => 'get-category',
                                                                    ],
                                                                ],
                                                            ],
                                                            'update-category' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'save-category',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::EVENTS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                            'delete-category' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'delete',
                                                                    'defaults' => [
                                                                        'action' => 'delete-category',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::EVENTS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'add-category' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'save-category',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EVENTS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'event' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'event[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\EventController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::EVENTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'event' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':event_id[/]',
                                                    'constraints' => [
                                                        'event_id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'search-words' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'search-word[/]',
                                                            'constraints' => [
                                                                'event_id' => '[\d]*',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'search-words' => [
                                                                'type' => Segment::class,
                                                                'options' => [
                                                                    'route' => ':search_word_id[/]',
                                                                    'constraints' => [
                                                                        'search_word_id' => '[\d]*',
                                                                    ],
                                                                ],
                                                                'child_routes' => [
                                                                    'update-search-word' => [
                                                                        'type' => Method::class,
                                                                        'options' => [
                                                                            'verb' => 'put',
                                                                            'defaults' => [
                                                                                'action' => 'save-search-words',
                                                                                'permissions' => [
                                                                                    [
                                                                                        'permission' => PermissionsTable::EVENTS,
                                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                                    ]
                                                                                ],
                                                                            ],
                                                                        ],
                                                                    ],
                                                                    'delete-search-word' => [
                                                                        'type' => Method::class,
                                                                        'options' => [
                                                                            'verb' => 'delete',
                                                                            'defaults' => [
                                                                                'action' => 'delete-search-word',
                                                                                'permissions' => [
                                                                                    [
                                                                                        'permission' => PermissionsTable::EVENTS,
                                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                                    ]
                                                                                ],
                                                                            ],
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                            'add-search-word' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'post',
                                                                    'defaults' => [
                                                                        'action' => 'save-search-words',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::EVENTS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'get-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-event',
                                                            ],
                                                        ],
                                                    ],
                                                    'update-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save-event',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EVENTS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'category' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'category[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'change-category' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'patch',
                                                                    'defaults' => [
                                                                        'action' => 'change-category',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::EVENTS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-event',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EVENTS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'add-event' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save-event',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::EVENTS,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'users' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'users[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyUsersController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::USERS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-users' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-company-users',
                                                    ],
                                                ],
                                            ],
                                            'agents' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'agents[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-agents' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-agents',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'managers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'managers[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-agents' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-managers',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'user' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'user[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyUsersController::class,
                                            ],
                                        ],
                                        'child_routes' => [
                                            'active-user' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\CompanyController::class,
                                                        'action' => 'get-user',
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'save-user' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save-company-user',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::USERS,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'user' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':user_id[/]',
                                                    'constraints' => [
                                                        'user_id' => '[\d]+',
                                                    ],
                                                    'defaults' => [
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                                            Controller\V0\BaseController::CHECK_USER_ACCESS,
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'activity' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'activity[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'activate-user' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'change-company-user-activity',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::USERS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                    'get-user' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-user',
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_USER_ACCESS,
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'update-user' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save-company-user',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::USERS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-user' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete-company-user',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::USERS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'notifications' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'notifications[/]',
                                                    'defaults' => [
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'notifications-count' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'count[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'get' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'get',
                                                                    'defaults' => [
                                                                        'action' => 'get-user-notifications-count',
                                                                    ],
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                    'get-notifications' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-user-notifications',
                                                                'controller' => Controller\V0\UserNotificationController::class,
                                                            ],
                                                        ],
                                                    ],
                                                    'save-notifications' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'save-user-notifications',
                                                                'controller' => Controller\V0\UserNotificationController::class,
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'structure' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'structure[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::USERS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-company-structure' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-company-structure',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'details' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'details[/]',
                                        ],
                                        'child_routes' => [
                                            'get-company-details' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-company-details',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::COMPANY,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ]
                                                    ],
                                                ]
                                            ],
                                            'save-company-details' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'save-company-details',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::COMPANY,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'clients' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'clients[/]',
                                        ],
                                        'child_routes' => [
                                            'get-clients' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-company-clients',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CLIENTS,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'statuses' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'statuses[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-clients-statuses' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-clients-statuses',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CLIENTS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ],
                                            'countries' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'countries[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-clients-countries' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-clients-countries',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CLIENTS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ],
                                            'sources' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'sources[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-clients-sources' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-clients-sources',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CLIENTS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ],
                                            'campaign-ids' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'campaign-ids[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-clients-campaign-ids' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-clients-campaign-ids',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CLIENTS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'calls' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls[/]',
                                        ],
                                        'child_routes' => [
                                            'statuses' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'statuses[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-statuses' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-calls-statuses',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CALL,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'languages' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'languages[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-languages' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-company-calls-languages',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CALL,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'vocabulary' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'vocabulary[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyVocabularyController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::COMPANY,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-vocabulary' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-vocabulary',
                                                    ],
                                                ],
                                            ],
                                            'word' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'word[/]',
                                                    'constraints' => [
                                                        'word_id' => '[\d]+',
                                                    ],
                                                    'defaults' => [
                                                        'controller' => Controller\V0\CompanyVocabularyController::class,
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::COMPANY,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'add-word' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'save-word',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::COMPANY,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],

                                                            ],
                                                        ],
                                                    ],
                                                    'word' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':word_id[/]',
                                                            'constraints' => [
                                                                'word_id' => '[\d]+',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'update' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'save-word',
                                                                    ],
                                                                ],
                                                            ],
                                                            'delete' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'delete',
                                                                    'defaults' => [
                                                                        'action' => 'delete-word',
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'llm-events' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'llm-events[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyLlmEventController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-events' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-events',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'llm-event' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'llm-event[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyLlmEventController::class,
                                            ],
                                        ],
                                        'child_routes' => [
                                            'llm-event' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-event',
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'connect-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                                                ],
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::EVENTS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                                'action' => 'connect-event',
                                                            ],
                                                        ],
                                                    ],
                                                    'disconnect-event' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'disconnect-event',
                                                                'access-checks' => [
                                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ]
                                            ],
                                        ],
                                    ],
                                    'industries' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'industries[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyIndustryController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-industries' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-industries',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'industry' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'industry[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyIndustryController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'industry' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'connect-industry' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'connect-industry',
                                                            ],
                                                        ],
                                                    ],
                                                    'disconnect-industry' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'disconnect-industry',
                                                            ],
                                                        ],
                                                    ],
                                                ]
                                            ],
                                        ],
                                    ],
                                    'algo-apis' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'algo-apis[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyAlgoApiController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'algo-apis' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-algo-apis',
                                                    ],
                                                ],
                                            ],
                                            'direct-ner' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'direct-ner[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-direct-ner-algo-apis' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-direct-ner-algo-apis',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'algo-api' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'algo-api[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\CompanyAlgoApiController::class,
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'algo-api' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'connect-algo-api' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'connect-algo-api',
                                                            ],
                                                        ],
                                                    ],
                                                    'disconnect-algo-api' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'disconnect-algo-api',
                                                            ],
                                                        ],
                                                    ],
                                                ]
                                            ],
                                        ],
                                    ],
                                    'webhooks' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'webhooks[/]',
                                            'defaults' => [
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                                ],
                                                'controller' => WebhookController::class,
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-webhooks' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'GET',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::WEBHOOKS,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                        'action' => 'get-webhooks',
                                                    ],
                                                ],
                                            ],
                                            'types' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'types[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-types' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'GET',
                                                            'defaults' => [
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::WEBHOOKS,
                                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                                    ]
                                                                ],
                                                                'action' => 'get-types',
                                                            ],
                                                        ],
                                                    ]
                                                ],
                                            ],
                                            'add-webhook' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'POST',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::WEBHOOKS,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                        'action' => 'create-webhook',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'webhook' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'webhook[/]',
                                            'defaults' => [
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                    Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                                ],
                                                'controller' => WebhookController::class,
                                            ],
                                        ],
                                        'child_routes' => [
                                            'webhook' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':id[/]',
                                                    'constraints' => [
                                                        'id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'update-webhook' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'PUT',
                                                            'defaults' => [
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::WEBHOOKS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                                'action' => 'update-webhook',
                                                            ],
                                                        ],
                                                    ],
                                                    'delete-webhook' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'DELETE',
                                                            'defaults' => [
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::WEBHOOKS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                                'action' => 'delete-webhook',
                                                            ],
                                                        ],
                                                    ],
                                                ]
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'llm-events' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'llm-events[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\LlmEventController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get-events' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                ],
                                                'action' => 'get-events',
                                            ],
                                        ],
                                    ],
                                    'improve' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'improve[/]',
                                        ],
                                        'child_routes' => [
                                            'improve' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                        ],
                                                        'action' => 'improve-event',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'add-event' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'save-event',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'llm-event' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'llm-event[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\LlmEventController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'llm-event' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':id[/]',
                                            'constraints' => [
                                                'id' => '[\d]*',
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get-event' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-event',
                                                    ],
                                                ],
                                            ],
                                            'update-event' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'save-event',
                                                    ],
                                                ],
                                            ],
                                            'delete-event' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'delete',
                                                    'defaults' => [
                                                        'action' => 'delete-event',
                                                    ],
                                                ],
                                            ]
                                        ]
                                    ],
                                ],
                            ],
                            'industries' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'industries[/]',
                                ],
                                'child_routes' => [
                                    'get-industries' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'access-checks' => [
                                                    Controller\V0\BaseController::CHECK_API_ACCESS,
                                                    Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                ],
                                                'controller' => Controller\V0\IndustriesController::class,
                                                'action' => 'get-industries',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'industry' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'industry[/]',
                                ],
                                'child_routes' => [
                                    'industry' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':id[/]',
                                            'constraints' => [
                                                'id' => '[\d]*',
                                            ],
                                        ],
                                        'child_routes' => [
                                            'algo-apis' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'algo-apis[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\IndustryAlgoApiController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'algo-apis' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-algo-apis',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'llm-events' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'llm-events[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\IndustryLlmEventController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-events' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-events',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'llm-event' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'llm-event[/]',
                                                    'defaults' => [
                                                        'controller' => Controller\V0\IndustryLlmEventController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_ACCESS,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'llm-event' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':llm_event_id[/]',
                                                            'constraints' => [
                                                                'llm_event_id' => '[\d]*',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'get-event' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'get',
                                                                    'defaults' => [
                                                                        'action' => 'get-event',
                                                                    ],
                                                                ],
                                                            ],
                                                            'connect-event' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'post',
                                                                    'defaults' => [
                                                                        'action' => 'connect-event',
                                                                    ],
                                                                ],
                                                            ],
                                                            'disconnect-event' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'delete',
                                                                    'defaults' => [
                                                                        'action' => 'disconnect-event',
                                                                    ],
                                                                ],
                                                            ],
                                                        ]
                                                    ],
                                                ],
                                            ]
                                        ]
                                    ],
                                ],
                            ],
                            'roles' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'roles[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\RoleController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::ROLES,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get-roles' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-roles',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'role' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'role[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\RoleController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::ROLES,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'role' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':role_id[/]',
                                            'constraints' => [
                                                'role_id' => '[\d]*',
                                            ],
                                        ],
                                        'child_routes' => [
                                            'users' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'users[/]',
                                                ],
                                                'child_routes' => [
                                                    'bulk-add-users' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'bulk-save-users',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::USERS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ],
                                                                    [
                                                                        'permission' => PermissionsTable::ROLES,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'get-role' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-role',
                                                    ],
                                                ],
                                            ],
                                            'update-role' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'save-role',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::ROLES,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'delete-role' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'delete',
                                                    'defaults' => [
                                                        'action' => 'delete-role',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::ROLES,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'add-role' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'save-role',
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::ROLES,
                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'teams' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'teams[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\TeamController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::USERS,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get-teams' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-teams',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'team' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'team[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\TeamController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::USERS,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'team' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':team_id[/]',
                                            'constraints' => [
                                                'team_id' => '[\d]*',
                                            ],
                                        ],
                                        'child_routes' => [
                                            'users' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'users[/]',
                                                ],
                                                'child_routes' => [
                                                    'add-users' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'bulk-save-users',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::USERS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'get-team' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-team',
                                                    ],
                                                ],
                                            ],
                                            'update-team' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'put',
                                                    'defaults' => [
                                                        'action' => 'save-team',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::USERS,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'delete-team' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'delete',
                                                    'defaults' => [
                                                        'action' => 'delete-team',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::USERS,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'add-team' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'post',
                                            'defaults' => [
                                                'action' => 'save-team',
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::USERS,
                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'calls' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'calls[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\CallController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::CALL,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ],
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'get' => [
                                        'type' => Method::class,
                                        'options' => [
                                            'verb' => 'get',
                                            'defaults' => [
                                                'action' => 'get-calls',
                                            ],
                                        ],
                                    ],
                                    'manual-import' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'manual-import[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::MANUAL_CALL_UPLOAD,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'post' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'manual-import',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::MANUAL_CALL_UPLOAD,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'comments' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'comments[/]',
                                        ],
                                        'child_routes' => [
                                            'get-comments' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-comments',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'client-summary' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'client-summary[/]',
                                        ],
                                        'child_routes' => [
                                            'get-client-summary' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-client-summary',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'run-client-summary-update' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'run-update[/]',
                                                ],
                                                'child_routes' => [
                                                    'run-client-summary' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'run-client-summary-update',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CALL,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'call' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'call/',
                                    'defaults' => [
                                        'controller' => Controller\V0\CallController::class,
                                        'access-checks' => [
                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                            Controller\V0\BaseController::CHECK_ROLE_ACCESS,
                                            Controller\V0\BaseController::CHECK_CALL_ACCESS,
                                        ],
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::CALL,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ]
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'call' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => ':call_id[/]',
                                            'constraints' => [
                                                'call_id' => '[^\/]*',
                                            ],
                                        ],
                                        'child_routes' => [
                                            'paragraph' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'paragraph[/]',
                                                ],
                                                'child_routes' => [
                                                    'paragraph' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':paragraph_number[/]',
                                                            'constraints' => [
                                                                'paragraph_number' => '[\d]*',
                                                            ],
                                                        ],
                                                        'child_routes' => [
                                                            'get-paragraph' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'get',
                                                                    'defaults' => [
                                                                        'action' => 'get-paragraph',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL_PARAGRAPHS,
                                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                            'swap-speaker' => [
                                                                'type' => Segment::class,
                                                                'options' => [
                                                                    'route' => 'swap-speaker[/]',
                                                                ],
                                                                'child_routes' => [
                                                                    'patch' => [
                                                                        'type' => Method::class,
                                                                        'options' => [
                                                                            'verb' => 'patch',
                                                                            'defaults' => [
                                                                                'action' => 'swap-speaker',
                                                                                'permissions' => [
                                                                                    [
                                                                                        'permission' => PermissionsTable::CALL_SPEAKERS,
                                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                                    ]
                                                                                ],
                                                                            ],
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                            'update-paragraph' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'patch',
                                                                    'defaults' => [
                                                                        'action' => 'update-paragraph',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL_PARAGRAPHS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'comment' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'comment[/]',
                                                ],
                                                'child_routes' => [
                                                    'save-comment' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'save-comment',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CALL_COMMENTS,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'comment' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => ':comment_id[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'update-comment' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'put',
                                                                    'defaults' => [
                                                                        'action' => 'save-comment',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL_COMMENTS,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'set-as-read' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'read[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'read-call-comments' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'patch',
                                                                    'defaults' => [
                                                                        'action' => 'read-call-comments',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL_COMMENTS,
                                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'event-happening' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'event-happening[/]',
                                                ],
                                                'child_routes' => [
                                                    'change' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'change-event-happening',
                                                                'permissions' => [
                                                                    [
                                                                        'permission' => PermissionsTable::CALL,
                                                                        'level' => PermissionsTable::WRITE_PERMISSION,
                                                                    ]
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                    'confirm' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'confirm[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'change' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'post',
                                                                    'defaults' => [
                                                                        'action' => 'confirm-event-happening',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'reanalyze' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'reanalyze[/]',
                                                    'defaults' => [
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_GLOBAL_ADMIN_OR_FRONT_GLOBAL_ADMIN_ACCESS,
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'put' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'defaults' => [
                                                                'action' => 'reanalyze',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'review' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'review[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL_REVIEW_CALL,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'review-call' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'defaults' => [
                                                                'action' => 'review-call',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'transcribing' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'transcribing[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'transcribe-call' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'post',
                                                            'defaults' => [
                                                                'action' => 'transcribe-call',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'content' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'content[/]',
                                                ],
                                                'child_routes' => [
                                                    'get-content' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-call-content',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'download' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'download[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL_AUDIO,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'download' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'download-call',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'get-call' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'get-call',
                                                    ],
                                                ],
                                            ],
                                            'update-call' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'patch',
                                                    'defaults' => [
                                                        'action' => 'update-call',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'language' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'language[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'change-language' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'patch',
                                                            'defaults' => [
                                                                'action' => 'change-language',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'delete-call' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'delete',
                                                    'defaults' => [
                                                        'action' => 'delete-call',
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'speakers' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'speakers[/]',
                                                ],
                                                'child_routes' => [
                                                    'swap' => [
                                                        'type' => Segment::class,
                                                        'options' => [
                                                            'route' => 'swap[/]',
                                                        ],
                                                        'child_routes' => [
                                                            'patch' => [
                                                                'type' => Method::class,
                                                                'options' => [
                                                                    'verb' => 'patch',
                                                                    'defaults' => [
                                                                        'action' => 'swap-speakers',
                                                                        'permissions' => [
                                                                            [
                                                                                'permission' => PermissionsTable::CALL,
                                                                                'level' => PermissionsTable::WRITE_PERMISSION,
                                                                            ]
                                                                        ],
                                                                    ],
                                                                ],
                                                            ],
                                                        ],
                                                    ]
                                                ],
                                            ],
                                            'summarization' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'summarization[/]',
                                                    'defaults' => [
                                                        'controller' => CallSummarizationController::class,
                                                        'access-checks' => [
                                                            Controller\V0\BaseController::CHECK_API_ACCESS,
                                                            Controller\V0\BaseController::CHECK_ROBONOTE_API_ONLY_ACCESS,
                                                            Controller\V0\BaseController::CHECK_AUTHENTICATION,
                                                            Controller\V0\BaseController::CHECK_COMPANY_ACCESS,
                                                        ]
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get-summarization' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'get-call-summarization',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'report' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'report[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\ReportController::class,
                                    ],
                                ],
                                'child_routes' => [
                                    'clients-report' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'clients-report[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::CLIENTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'clients-report',
                                                    ],
                                                ],
                                            ],
                                            'csv' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'csv[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CLIENTS,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ],
                                                            [
                                                                'permission' => PermissionsTable::DOWNLOAD_CSV_REPORT,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'clients-report-csv',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'client-report' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'client-report/:client_id[/]',
                                            'constraints' => [
                                                'client_id' => '[^\/]*',
                                            ],
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::CLIENTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'client-report',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'calls-report' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls-report[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::CALL,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'calls-report',
                                                    ],
                                                ],
                                            ],
                                            'csv' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'csv[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ],
                                                            [
                                                                'permission' => PermissionsTable::DOWNLOAD_CSV_REPORT,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'calls-report-csv',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'get-filter-values-ranges' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'values-ranges[/]',
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'calls-report-values-ranges',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'calls' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::CALL,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'calls',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'agents-report' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'agents-report[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::USERS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'agents-report',
                                                    ],
                                                ],
                                            ],
                                            'calendar' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'calendar[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::USERS,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ],
                                                            [
                                                                'permission' => PermissionsTable::CALL,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'agents-report-calendar',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'agent-report' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'agent-report/:agent_id[/]',
                                            'constraints' => [
                                                'agent_id' => '[\d]+',
                                            ],
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::USERS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'agent-report',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'templates' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'templates[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\ReportTemplateController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::REPORTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'templates',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'template' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'template[/]',
                                            'defaults' => [
                                                'controller' => Controller\V0\ReportTemplateController::class,
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::REPORTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'post' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'post',
                                                    'defaults' => [
                                                        'action' => 'save',
                                                    ],
                                                ],
                                            ],
                                            'template' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => ':template_id[/]',
                                                    'constraints' => [
                                                        'template_id' => '[\d]*',
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'delete' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'delete',
                                                            'defaults' => [
                                                                'action' => 'delete',
                                                            ],
                                                        ],
                                                    ],
                                                    'put' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'put',
                                                            'defaults' => [
                                                                'action' => 'save',
                                                            ],
                                                        ],
                                                    ],
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'template',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'qc' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'qc[/]',
                                            'defaults' => [
                                                'permissions' => [
                                                    [
                                                        'permission' => PermissionsTable::REPORTS,
                                                        'level' => PermissionsTable::READ_PERMISSION,
                                                    ]
                                                ],
                                            ],
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'qc',
                                                    ],
                                                ],
                                            ],
                                            'get-filter-values-ranges' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'values-ranges[/]',
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'values-ranges',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                            'csv' => [
                                                'type' => Segment::class,
                                                'options' => [
                                                    'route' => 'csv[/]',
                                                    'defaults' => [
                                                        'permissions' => [
                                                            [
                                                                'permission' => PermissionsTable::DOWNLOAD_CSV_REPORT,
                                                                'level' => PermissionsTable::READ_PERMISSION,
                                                            ]
                                                        ],
                                                    ],
                                                ],
                                                'child_routes' => [
                                                    'get' => [
                                                        'type' => Method::class,
                                                        'options' => [
                                                            'verb' => 'get',
                                                            'defaults' => [
                                                                'action' => 'qc-csv',
                                                            ],
                                                        ],
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'search' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'search[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\SearchController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::SEARCH,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ],
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'calls' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-calls',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'calls-ids' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls-ids[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-calls-ids',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'agents' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'agents[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-agents',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'search-engine' => [
                                'type' => Segment::class,
                                'options' => [
                                    'route' => 'search-engine[/]',
                                    'defaults' => [
                                        'controller' => Controller\V0\SearchEngineController::class,
                                        'permissions' => [
                                            [
                                                'permission' => PermissionsTable::SEARCH,
                                                'level' => PermissionsTable::READ_PERMISSION,
                                            ],
                                        ],
                                    ],
                                ],
                                'child_routes' => [
                                    'calls' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-calls',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'calls-ids' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'calls-ids[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-calls-ids',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'paragraphs' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'paragraphs[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-paragraphs',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'clients' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'clients[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-clients',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'agents' => [
                                        'type' => Segment::class,
                                        'options' => [
                                            'route' => 'agents[/]',
                                        ],
                                        'child_routes' => [
                                            'get' => [
                                                'type' => Method::class,
                                                'options' => [
                                                    'verb' => 'get',
                                                    'defaults' => [
                                                        'action' => 'search-agents',
                                                    ],
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],
];
