<?php

declare(strict_types=1);

namespace STCall\Daemon;

use Carbon\Carbon;
use STAlgo\Service\AiSolutionsCommutatorService;
use STApi\Entity\Exception\ThirdPartyApiException;
use ST<PERSON>all\Entity\CallCollection;
use STCall\Service\CallService;
use STCall\Service\ClientSummaryService;
use STCompany\Service\CompanyService;
use STRabbit\Entity\AbstractDaemon;

class ClientSummaryDaemon extends AbstractDaemon
{
    public const string QUEUE = 'client-summary';
    public const string QUEUE_ERROR = 'client-summary-error';

    public function __construct(
        private readonly CallService $callService,
        private readonly CompanyService $companyService,
        private readonly ClientSummaryService $clientSummaryService,
        private readonly AiSolutionsCommutatorService $aiSolutionsCommutator
    ) {
    }

    public function handle(string $message): void
    {
        $data = json_decode($message, true);

        if (!isset($data['company_id']) || !isset($data['client_id'])) {
            throw new \InvalidArgumentException('Missing required parameters: company_id and client_id');
        }

        $company = $this->companyService->getCompany((int) $data['company_id']);
        $clientId = (string) $data['client_id'];

        $lastSummary = $this->clientSummaryService->getLastSummary(
            $company->getId(),
            $clientId,
        );

        $startDate = $lastSummary?->getCreated();
        $calls = $this->callService->getClientCallsByDateRange(
            $company,
            $clientId,
            $startDate,
        );

        if ($calls->isEmpty()) {
            return;
        }

        $this->generateSummaryFromCalls(
            $company->getId(),
            $clientId,
            $calls,
        );
    }

    private function generateSummaryFromCalls(
        int $companyId,
        string $clientId,
        CallCollection $calls,
    ): void
    {
        // Sort calls by time
        $calls->usort(function ($a, $b) {
            return $a->getTime()->lt($b->getTime()) ? -1 : 1;
        });
        // Get client summary with last_call_time less than call time of the earliest call
        $previousSummary = $this->clientSummaryService->getLastSummary(
            $companyId,
            $clientId,
            $calls->current()->getTime()
        );

        try {
            $summaryDTO = $this->aiSolutionsCommutator->getClientSummary($companyId, $calls, $previousSummary);
            $this->clientSummaryService->createClientSummaryFromDTO(
                $summaryDTO,
                $companyId,
                $clientId,
                $calls->last()->getTime()
            );

            return;
        } catch (ThirdPartyApiException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw new ThirdPartyApiException('Failed to generate client summary: ' . $e->getMessage());
        }
    }
}
