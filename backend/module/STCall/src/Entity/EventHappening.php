<?php

declare(strict_types=1);

namespace STCall\Entity;

class EventHappening
{
    use \STLib\Mvc\Hydrator\BaseHydratorTrait;

    /**
     *
     * @var \STCompany\Entity\Event\Event|null
     */
    protected \STCompany\Entity\Event\Event $event;

    /**
     *
     * @var string
     */
    protected string $mainPointPhrase;

    /**
     *
     * @var string
     */
    protected string $enMainPointPhrase;

    /**
     *
     * @var string
     */
    protected string $text = '';

    /**
     *
     * @var string
     */
    protected string $enText = '';

    /**
     *
     * @var ?float
     */
    protected ?float $score = null;

    /**
     *
     * @var bool
     */
    protected bool $isConfirmed = false;

    /**
     *
     * @var bool
     */
    protected bool $isChanged = false;

    /**
     *
     * @var bool
     */
    protected bool $isDeleted = false;

    /**
     *
     * @var EventHappening\HistoryRecordCollection
     */
    protected EventHappening\HistoryRecordCollection $historyRecords;

    /**
     *
     * @return \STCompany\Entity\Event\Event
     */
    public function getEvent(): \STCompany\Entity\Event\Event
    {
        return $this->event;
    }

    /**
     *
     * @return string
     */
    public function getMainPointPhrase(): string
    {
        return $this->mainPointPhrase;
    }

    /**
     *
     * @return string
     */
    public function getEnMainPointPhrase(): string
    {
        return $this->enMainPointPhrase;
    }

    /**
     *
     * @return string
     */
    public function getText(): string
    {
        return $this->text;
    }

    /**
     *
     * @return string
     */
    public function getEnText(): string
    {
        return $this->enText;
    }

    /**
     *
     * @return bool
     */
    public function getIsConfirmed(): bool
    {
        if (is_null($this->getEvent()) || !$this->getEvent()->isConfirmNeeded()) {
            return true;
        }
        return $this->isConfirmed;
    }

    /**
     *
     * @return bool
     */
    public function getIsChanged(): bool
    {
        return $this->isChanged;
    }

    /**
     *
     * @return bool
     */
    public function getIsDeleted(): bool
    {
        return $this->isDeleted;
    }

    /**
     *
     * @return EventHappening\HistoryRecordCollection
     */
    public function getHistoryRecords(): EventHappening\HistoryRecordCollection
    {
        if (!isset($this->historyRecords)) {
            $this->historyRecords = new EventHappening\HistoryRecordCollection();
        }
        return $this->historyRecords;
    }

    /**
     *
     * @param \STCompany\Entity\Event\Event $event
     * @return EventHappening
     */
    public function setEvent(\STCompany\Entity\Event\Event $event): EventHappening
    {
        $this->event = $event;
        return $this;
    }

    /**
     *
     * @param string $mainPointPhrase
     * @return EventHappening
     */
    public function setMainPointPhrase(string $mainPointPhrase): EventHappening
    {
        $this->mainPointPhrase = $mainPointPhrase;
        return $this;
    }

    /**
     *
     * @param string $enMainPointPhrase
     * @return EventHappening
     */
    public function setEnMainPointPhrase(string $enMainPointPhrase): EventHappening
    {
        $this->enMainPointPhrase = $enMainPointPhrase;
        return $this;
    }

    /**
     * @return ?float
     */
    public function getScore(): ?float
    {
        return $this->score;
    }

    /**
     * @param float $score
     * @return EventHappening
     */
    public function setScore(float $score): EventHappening
    {
        $this->score = $score;
        return $this;
    }

    /**
     *
     * @param string $text
     * @return EventHappening
     */
    public function setText(string $text): EventHappening
    {
        $this->text = $text;
        return $this;
    }

    /**
     *
     * @param string $enText
     * @return EventHappening
     */
    public function setEnText(string $enText): EventHappening
    {
        $this->enText = $enText;
        return $this;
    }

    /**
     *
     * @param bool $isConfirmed
     * @return EventHappening
     */
    public function setIsConfirmed(bool $isConfirmed): EventHappening
    {
        $this->isConfirmed = $isConfirmed;
        return $this;
    }

    /**
     * @param bool $isChanged
     * @return EventHappening
     */
    public function setIsChanged(bool $isChanged): EventHappening
    {
        $this->isChanged = $isChanged;
        return $this;
    }

    /**
     *
     * @param bool $isDeleted
     * @return EventHappening
     */
    public function setIsDeleted(bool $isDeleted): EventHappening
    {
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     *
     * @param EventHappening\HistoryRecordCollection $historyRecords
     * @return EventHappening
     */
    public function setHistoryRecords(EventHappening\HistoryRecordCollection $historyRecords): EventHappening
    {
        $this->historyRecords = $historyRecords;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getHighlight(): string
    {
        if (empty($this->getMainPointPhrase())) {
            return $this->getText();
        }
        if (!str_contains(strtolower($this->getText()), strtolower($this->getMainPointPhrase()))) {
            return $this->getText();
        }
        return $this->getMainPointPhrase();
    }

    /**
     *
     * @return string
     */
    public function getEnHighlight(): string
    {
        if (empty($this->getEnMainPointPhrase())) {
            return $this->getEnText();
        }
        if (!str_contains(strtolower($this->getEnText()), strtolower($this->getEnMainPointPhrase()))) {
            return $this->getEnText();
        }
        return $this->getEnMainPointPhrase();
    }

    /**
     *
     * @param bool $isConfirmed
     * @return bool|EventHappening
     */
    public function isConfirmed(bool $isConfirmed = null): bool|EventHappening
    {
        if (is_null($isConfirmed)) {
            return $this->getIsConfirmed();
        }
        $this->isConfirmed = $isConfirmed;
        return $this;
    }

    /**
     *
     * @param bool $isChanged
     * @return bool|EventHappening
     */
    public function isChanged(bool $isChanged = null): bool|EventHappening
    {
        if (is_null($isChanged)) {
            return $this->isChanged;
        }
        $this->isChanged = $isChanged;
        return $this;
    }

    /**
     *
     * @param bool $isDeleted
     * @return bool|EventHappening
     */
    public function isDeleted(bool $isDeleted = null): bool|EventHappening
    {
        if (is_null($isDeleted)) {
            return $this->getIsDeleted();
        }
        $this->isDeleted = $isDeleted;
        return $this;
    }

    /**
     *
     * @return array
     */
    public function toArray(): array
    {
        $result = $this->extract($this);
        $result['event'] = $this->getEvent()->toArray();
        $result['history_records'] = $this->getHistoryRecords()->toArray();
        return $result;
    }
}
