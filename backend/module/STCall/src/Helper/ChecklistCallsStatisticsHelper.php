<?php

declare(strict_types=1);

namespace STCall\Helper;

use ST<PERSON>all\Data\CallsChecklistsPointsTable;

class ChecklistCallsStatisticsHelper
{
    public static function calculateScore(array $data, string $key, bool $isPercentage = false): float
    {
        $total = array_reduce($data, static function ($carry, $item) use ($key) {
            return $carry + $item[$key];
        }, 0);

        $checklistPointsWithoutNotEncountered = array_filter($data, static function ($item) {
            return !isset($item['status']) || (string) $item['status'] !== CallsChecklistsPointsTable::STATUS_NOT_ENCOUNTERED;
        });

        $score = count($checklistPointsWithoutNotEncountered) > 0
            ? $total / count($checklistPointsWithoutNotEncountered)
            : 0;

        if ($isPercentage) {
            $score *= 100;
        }

        return round($score);
    }
}
