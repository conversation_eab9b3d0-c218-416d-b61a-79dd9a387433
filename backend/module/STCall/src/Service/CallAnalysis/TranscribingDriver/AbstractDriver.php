<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis\TranscribingDriver;

use STCall\Entity\Call;
use STCall\Service\AwsTrait;
use STCall\Service\CallAnalysis\TranscribingDriver\ResponseConverter\ResponseToCollectionConverter;
use STCompany\Entity\CompanyVocabularyCollection;
use STLog\Service\ProviderLogger;
use STRoboTruck\Service\DataCollection\DataCollector;

abstract class AbstractDriver
{
    use AwsTrait;
    use ProviderLogger;

    /**
     *
     * @var array
     */
    protected array $driverConfig = [];

    /**
     *
     * @var CompanyVocabularyCollection
     */
    protected CompanyVocabularyCollection $companyVocabularyCollection;

    /**
     *
     * @var Call
     */
    protected Call $call;

    public function __construct(private readonly DataCollector $dataCollector)
    {
    }

    /**
     *
     * @return array
     */
    public function getDriverConfig(): array
    {
        return $this->driverConfig;
    }

    /**
     *
     * @return Call
     */
    public function getCall(): Call
    {
        return $this->call;
    }

    /**
     *
     * @param array $driverConfig
     * @return AbstractDriver
     */
    public function setDriverConfig(array $driverConfig): AbstractDriver
    {
        $this->driverConfig = $driverConfig;
        return $this;
    }

    /**
     *
     * @param Call $call
     * @return AbstractDriver
     */
    public function setCall(Call $call): AbstractDriver
    {
        $this->call = $call;
        return $this;
    }

    /**
     *
     * @param int $timeInMinutes
     * @return string
     */
    protected function getFileLink(int $timeInMinutes = 60): string
    {
        return $this->getPreSignedUrl($this->call->getS3FilePath(), $timeInMinutes);
    }

    /**
     * @return string
     */
    protected function getFileContent(): string
    {
        return $this->getFileFromS3ByFileName($this->call->getS3FilePath());
    }

    /**
     *
     * @param CompanyVocabularyCollection $companyVocabularyCollection
     * @return AbstractDriver
     */
    public function setCompanyVocabulary(CompanyVocabularyCollection $companyVocabularyCollection): AbstractDriver
    {
        $this->companyVocabularyCollection = $companyVocabularyCollection;
        return $this;
    }

    /**
     *
     * @return CompanyVocabularyCollection
     */
    public function getCompanyVocabulary(): CompanyVocabularyCollection
    {
        return $this->companyVocabularyCollection;
    }

    /**
     *
     * @return ResponseToCollectionConverter
     */
    protected function getResponseToCollectionConverter(): ResponseConverter\ResponseToCollectionConverter
    {
        return new ResponseConverter\ResponseToCollectionConverter();
    }

    /**
     * @param string $jobId
     * @param string $driverName
     * @return void
     */
    protected function createTranscribingRequestLog(string $jobId, string $driverName): void
    {
        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_ANALYZE_TRANSCRIBING_DRIVER,
            $driverName . ' transcribing request',
            [
                'id' => $jobId,
                'company_id' => $this->company->getId(),
                'call_id' => $this->call->getId(),
            ],
            'api-calls-logs'
        );
    }
}
