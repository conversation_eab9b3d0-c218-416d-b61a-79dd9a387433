<?php

declare(strict_types=1);

namespace STCall\Service\CallAnalysis;

interface StepInterface
{
    /**
     *
     * @param int $companyId
     * @return bool
     */
    public function run(int $companyId): bool;

    /**
     *
     * @return string
     */
    public function getNextStepQueue(): string;

    /**
     *
     * @param array $options
     * @return StepInterface
     */
    public function applyOptions(array $options = []): StepInterface;

    /**
     *
     * @return string
     */
    public function getQueueName(): string;

    /**
     *
     * @return string
     */
    public function getErrorQueueName(): string;
}
