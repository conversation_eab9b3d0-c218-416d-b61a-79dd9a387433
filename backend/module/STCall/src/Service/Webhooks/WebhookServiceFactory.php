<?php

declare(strict_types=1);

namespace STCall\Service\Webhooks;

use RuntimeException;
use STCall\Service\CallAnalysis\AlgoEventsStep;
use STCall\Service\CallAnalysis\ChecklistStep;
use STCall\Service\CallAnalysis\LlmEventsDetectionStep;
use STCall\Service\CallAnalysis\SummarizationStep;
use STCall\Service\CallAnalysis\TranscribingJobCollectionStep;
use STCall\Service\CallAnalysis\TranscribingStep;
use STCall\Service\CallAnalysis\TranscribingWhisperStep;
use STCall\Service\CallAnalysis\TranslationStep;
use STCall\Service\Webhooks\Interfaces\WebhookServiceInterface;
use STCall\Service\Webhooks\Services\ChecklistsWebhookService;
use STCall\Service\Webhooks\Services\EventsWebhookService;
use STCall\Service\Webhooks\Services\ParagraphsWebhookService;
use STCall\Service\Webhooks\Services\SummarizationWebhookService;
use STCall\Service\Webhooks\Services\TranslationWebhookService;

class WebhookServiceFactory
{
    public function __construct(
        private readonly ParagraphsWebhookService $paragraphsWebhookService,
        private readonly TranslationWebhookService $translationWebhookService,
        private readonly EventsWebhookService $eventsWebhookService,
        private readonly ChecklistsWebhookService $checklistsWebhookService,
        private readonly SummarizationWebhookService $summarizationWebhookService,
    ) {
    }

    public function create(string $source): WebhookServiceInterface
    {
        return match ($source) {
            TranscribingStep::CALL_TRANSCRIBING_QUEUE => $this->paragraphsWebhookService,
            TranscribingJobCollectionStep::CALL_TRANSCRIBING_JOB_COLLECTION_QUEUE => $this->paragraphsWebhookService,
            TranscribingWhisperStep::CALL_TRANSCRIBING_QUEUE => $this->paragraphsWebhookService,
            TranslationStep::CALL_TRANSLATION_QUEUE => $this->translationWebhookService,
            AlgoEventsStep::CALL_ALGO_EVENTS_QUEUE => $this->eventsWebhookService,
            ChecklistStep::CALL_CHECKLIST_QUEUE => $this->checklistsWebhookService,
            SummarizationStep::CALL_SUMMARIZATION_QUEUE => $this->summarizationWebhookService,
            default => throw new RuntimeException('Unknown source: ' . $source),
        };
    }
}
