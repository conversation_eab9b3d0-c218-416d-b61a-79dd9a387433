<?php

declare(strict_types=1);

namespace STCall\Service;

use Carbon\Carbon;
use PhpAmqpLib\Message\AMQPMessage;
use ST<PERSON>all\Daemon\ClientSummaryDaemon;
use ST<PERSON>all\Data\ClientSummariesTable;
use ST<PERSON>all\Entity\ClientSummary;
use ST<PERSON>all\Entity\DTO\ClientSummaryDTO;
use STLib\Mvc\Hydrator\BaseHydrator;
use STRabbit\Service\RabbitService;

class ClientSummaryService
{
    public function __construct(
        private readonly ClientSummariesTable $clientSummariesTable,
        private readonly RabbitService $rabbitService,
        private readonly BaseHydrator $hydrator
    ) {
    }

    public function createClientSummaryFromDTO(
        ClientSummaryDTO $clientSummaryDTO,
        int $companyId,
        string $clientId,
        Carbon $lastCallTime = null
    ): ClientSummary {
        $clientSummary = new ClientSummary();
        $clientSummary
            ->setCompanyId($companyId)
            ->setClientId($clientId)
            ->setPrimaryPurpose($clientSummaryDTO->primaryPurpose)
            ->setDealSize($clientSummaryDTO->dealSize)
            ->setTimeline($clientSummaryDTO->timeline)
            ->setDealStatus($clientSummaryDTO->dealStatus)
            ->setNextStep($clientSummaryDTO->nextStep)
            ->setCustomerSentiment($clientSummaryDTO->customerSentiment)
            ->setClientDiscoveryParameters($clientSummaryDTO->clientDiscoveryParameters)
            ->setCustomerProblems($clientSummaryDTO->customerProblems)
            ->setBusinessOpportunities($clientSummaryDTO->businessOpportunities)
            ->setRisks($clientSummaryDTO->risks)
            ->setAgentPerformance($clientSummaryDTO->agentPerformance)
            ->setInteractionNotes($clientSummaryDTO->interactionNotes)
            ->setLastCallTime($lastCallTime ?? Carbon::now())
            ->setCreated(Carbon::now());

        $this->clientSummariesTable->create($clientSummary);

        return $clientSummary;
    }

    public function getLastSummary(int $companyId, string $clientId, ?Carbon $date = null): ClientSummary|null
    {
        $clientSummaryData = $this->clientSummariesTable->getLastSummary($companyId, $clientId, $date);

        if (is_null($clientSummaryData)) {
            return null;
        }

        /** @var ClientSummary $clientSummary */
        $clientSummary = $this->hydrator->hydrate(
            $clientSummaryData,
            new ClientSummary(),
        );

        return $clientSummary;
    }

    public function publishClientSummaryUpdate(int $companyId, string $clientId): void
    {
        $this->rabbitService->getChannel()->basic_publish(
            new AMQPMessage(json_encode([
                'company_id' => $companyId,
                'client_id' => $clientId,
            ])),
            routing_key: ClientSummaryDaemon::QUEUE
        );
    }
}
