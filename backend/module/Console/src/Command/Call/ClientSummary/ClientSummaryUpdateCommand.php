<?php

declare(strict_types=1);

namespace Console\Command\Call\ClientSummary;

use Console\Command\BaseCommand;
use PhpAmqpLib\Message\AMQPMessage;
use STCall\Daemon\ClientSummaryDaemon;
use STCall\Service\CallService;
use STCompany\Service\CompanyService;
use STRabbit\Service\RabbitService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'call:client-summary:update',
    description: 'Run client summary update',
)]
final class ClientSummaryUpdateCommand extends BaseCommand
{
    protected function launch(InputInterface $input, OutputInterface $output): int
    {
        /**@var RabbitService $rabbit */
        $rabbit = $this->getServiceManager()->get(RabbitService::class);
        /** @var CompanyService $companyService */
        $companyService = $this->getServiceManager()->get(CompanyService::class);
        /** @var CallService $callService */
        $callService = $this->getServiceManager()->get(CallService::class);

        foreach ($companyService->getCompanyIdsWithClientSummaryEnabled() as $companyId) {
            $clientIds = $callService->getClientIdsByCompanyId($companyId);

            foreach ($clientIds as $clientId) {
                $message = new AMQPMessage(json_encode([
                    'company_id' => $companyId,
                    'client_id' => $clientId,
                ]));
                $rabbit->getChannel()->basic_publish($message, '', routing_key: ClientSummaryDaemon::QUEUE);
            }
        }

        return self::FAILURE;
    }
}
