<?php

declare(strict_types=1);

namespace STAlgo\Service;

use G<PERSON><PERSON><PERSON><PERSON>p\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use JsonException;
use STAlgo\Entity\AlgoApi;
use STAlgo\Service\AlgoEvents\RequestCreation\EventsAlgoApiRequestInterface;
use STAlgo\Service\Interfaces\ConfigurationInterface;
use STAlgo\Service\ParamsBuilding\RequestParams;
use STRoboTruck\Service\DataCollection\DataCollector;

class Client
{
    protected const string API_METHOD_GET_EVENTS = '/get_events';
    protected const string API_METHOD_SPEAKERS_ROLES_DETECTION = '/api/classV1';
    protected const string API_METHOD_CHECKLIST = '/api/checklist/checklistV5';
    protected const string API_METHOD_TRANSLATE = '/api/translation/translationV1';
    protected const string API_METHOD_SUMMARIZATION = '/api/algo/summarizationV3';
    protected const string API_METHOD_IMPROVE_LLM_EVENT = '/api/algo/tuningV1/promptV1';
    protected const string API_METHOD_IMPROVE_CHECKLIST_POINT = '/api/algo/tuningV1/checklistV5';
    protected const string API_METHOD_GET_MODELS_WITH_DESCRIPTIONS = '/api/get_models_with_descriptions';
    protected const string API_METHOD_DETECT_LANGUAGE_BY_TEXT = '/api/algo/detect-language-by-text';
    protected const string API_METHOD_CLIENT_SUMMARIZATION = '/api/algo/clientSummarizationV1';
    protected const string TRANSLATION_DETECT_LANGUAGE_URL = '/detect-language';

    /**
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     *
     * @var string
     */
    protected string $algoApiKey;

    /**
     *
     * @var string
     */
    protected string $env;

    /**
     *
     * Class constructor
     */
    public function __construct(
        private readonly ConfigurationInterface $configuration,
        private readonly DataCollector $dataCollector,
    ) {
        $this->algoApiKey = getenv('ALGO_API_KEY');
        $this->env = getenv('APP_ENV');
        $this->apiUrl = getenv('ALGO_API_URL');
    }

    /**
     *
     * @param AlgoApi $algoApi
     * @return string
     * @throws GuzzleException
     */
    public function getEvents(AlgoApi $algoApi): string
    {
        $response = $this->getGuzzleClient()->get($this->apiUrl . $algoApi->getPath() . static::API_METHOD_GET_EVENTS, [
            'headers' => $this->getHeaders(),
            'query' => [
                'env' => $this->env,
            ],
        ]);

        return $response->getBody()->getContents();
    }

    /**
     *
     * @param array $params
     * @return string
     * @throws GuzzleException
     */
    public function getSpeakers(array $params): string
    {
        $response = $this->getGuzzleClient()->post(
            $this->apiUrl . self::API_METHOD_SPEAKERS_ROLES_DETECTION,
            [
                'body' => json_encode($params),
                'headers' => $this->getHeaders(),
                'query' => [
                    'env' => $this->env,
                ],
            ]
        );

        return $response->getBody()->getContents();
    }

    /**
     *
     * @param array $params
     * @return array
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getChecklistResult(array $params): array
    {
        $apiUrl = $this->configuration->get('algo')['checklist_api_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];
        $jsonParams = json_encode($params);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_CHECKLIST,
            [
                'body' => $jsonParams,
                'headers' => $headers,
                'query' => [
                    'env' => $this->env,
                ],
                'timeout' => 180
            ]
        );

        $responseContent = $response->getBody()->getContents();
        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_CHECKLIST_RESULT,
            'Call checklist result',
            [
                'request' => $jsonParams,
                'response' => $responseContent,
            ]
        );

        return json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @param EventsAlgoApiRequestInterface $algoApiRequest
     * @return string
     * @throws GuzzleException
     */
    public function analyze(EventsAlgoApiRequestInterface $algoApiRequest): string
    {
        $response = $this->getGuzzleClient()->post(
            $algoApiRequest->getUrl(),
            [
                'body' => json_encode($algoApiRequest->getParams()),
                'headers' => $this->getHeaders(),
                'query' => [
                    'env' => $algoApiRequest->getEnv() ?? $this->env,
                ],
            ]
        );

        return $response->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    public function getModels(): array
    {
        $apiUrl = $this->configuration->get('algo')['api_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()
            ->get(
                $apiUrl . static::API_METHOD_GET_MODELS_WITH_DESCRIPTIONS,
                ['headers' => $headers]
            );

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function translate(array $params): array
    {
        $apiUrl = $this->configuration->get('algo')['translation_api_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_TRANSLATE,
            [
                'body' => json_encode($params),
                'headers' => $headers,
                'timeout' => 300
            ]
        );

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function getCallSummarization(RequestParams $params): array
    {
        $apiUrl = $this->configuration->get('algo')['summarization_api_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_SUMMARIZATION,
            [
                'body' => json_encode($params->toArray()),
                'headers' => $headers,
                'timeout' => 120
            ]
        );

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function improveLlmEvent(string $name, string $description): array
    {
        $apiUrl = $this->configuration->get('algo')['improve_llm_event_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $data = [
            'event' => [
                'title' => $name,
                'description' => $description,
            ],
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_IMPROVE_LLM_EVENT,
            [
                'body' => json_encode($data),
                'headers' => $headers,
                'timeout' => 120
            ]
        );

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function improveChecklistPoint(array $params): array
    {
        $apiUrl = $this->configuration->get('algo')['improve_checklist_point_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_IMPROVE_CHECKLIST_POINT,
            [
                'body' => json_encode($params),
                'headers' => $headers,
                'timeout' => 120
            ]
        );

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function detectLanguageByText(string $text): array
    {
        $response = $this->getGuzzleClient()->post(
            $this->configuration->get('algo')['language_detection_api_url'] . self::API_METHOD_DETECT_LANGUAGE_BY_TEXT,
            [
                'body' => json_encode(['text' => $text]),
                'headers' => $this->getHeaders(),
                'query' => [
                    'env' => $this->env,
                ],
            ]
        );

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function detectLanguage(string $content): array
    {
        $client = new GuzzleClient([
            'base_uri' => $this->configuration->get('algo')['language_detection_by_content_api_url'],
        ]);

        $response = $client->post(self::TRANSLATION_DETECT_LANGUAGE_URL, [
            'body' => json_encode(['content' => base64_encode($content)]),
            'headers' => $this->getHeaders(),
        ]);

        return json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);
    }

    public function getClientSummary(array $params): array
    {
        $apiUrl = $this->configuration->get('algo')['language_detection_api_url'];
        $algoApiKey = $this->configuration->get('algo')['api_key'];
        $jsonParams = json_encode($params);

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $algoApiKey,
        ];

        $response = $this->getGuzzleClient()->post(
            $apiUrl . self::API_METHOD_CLIENT_SUMMARIZATION,
            [
                'body' => $jsonParams,
                'headers' => $headers,
                'query' => [
                    'env' => $this->env,
                ],
                'timeout' => 180
            ]
        );

        $responseContent = $response->getBody()->getContents();
        $this->dataCollector->collect(
            DataCollector::EVENT_CALL_CLIENT_SUMMARY,
            'Client summary result',
            [
                'request' => $jsonParams,
                'response' => $responseContent,
            ]
        );

        return json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     *
     * @return GuzzleClient
     */
    protected function getGuzzleClient(): GuzzleClient
    {
        return new GuzzleClient();
    }

    /**
     *
     * @return array
     */
    protected function getHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->algoApiKey,
        ];
    }
}
