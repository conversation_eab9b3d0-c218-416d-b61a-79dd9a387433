<?php

declare(strict_types=1);

namespace STAlgo\Service\AlgoEvents\RequestCreation;

use ReflectionException;
use ST<PERSON>all\Data\CallsAlgoEventsTable;
use STCall\Entity\AlgoEvent;
use ST<PERSON>all\Entity\AlgoEventCollection;
use ST<PERSON>all\Entity\Call;
use STCompany\Entity\Company;
use STLib\Mvc\Hydrator\Hydrator;

class ResponseToAlgoEventConverter
{
    public function __construct(private readonly Hydrator $hydrator)
    {
    }

    /**
     * @param AlgoEventCollection $algoEventCollection
     * @param string $jsonResponse
     * @param Company $company
     * @param Call $call
     * @param EventsAlgoApiRequestInterface $algoApiRequest
     * @return void
     * @throws ReflectionException
     */
    public function convert(
        AlgoEventCollection $algoEventCollection,
        string $jsonResponse,
        Company $company,
        Call $call,
        EventsAlgoApiRequestInterface $algoApiRequest,
    ): void {
        $eventData = json_decode($jsonResponse);

        if (isset($eventData->results->segments) && isset($eventData->status)) {
            if ($eventData->status !== 'ok') {
                throw new \RuntimeException('Invalid response from algo API "' . $jsonResponse . '"');
            }

            foreach ($eventData->results->segments as $segment) {
                if ($segment->class !== CallsAlgoEventsTable::NEUTRAL_EVENT) {
                    $algoEvent = $this->hydrator->hydrateClass(
                        [
                            'company_id' => $company->getId(),
                            'call_id' => $call->getId(),
                            'paragraph_number' => $segment->id,
                            'algo_api_id' => $algoApiRequest->getAlgoApiId(),
                            'industry_id' => $algoApiRequest->getIndustryId(),
                            'event' => $segment->class,
                            'call_time' => $call->getTime(),
                            'score' => $segment->score,
                            'main_point_phrase' => $segment->sentence,
                            'main_point_location' => [],
                        ],
                        AlgoEvent::class
                    );
                    $algoEventCollection->add($algoEvent);
                }
            }
        }
    }
}
