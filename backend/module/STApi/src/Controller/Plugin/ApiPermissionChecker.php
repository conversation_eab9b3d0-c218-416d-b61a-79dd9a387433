<?php

declare(strict_types=1);

namespace STApi\Controller\Plugin;

use Api\Controller\V0\BaseController;
use <PERSON>inas\Mvc\Controller\Plugin\AbstractPlugin;
use STApi\Entity\Application;
use STApi\Entity\Exception\UnauthorizedApiException;
use STCompany\Entity\Company;

/**
 * @method BaseController getController
 */
class ApiPermission<PERSON>hecker extends AbstractPlugin
{
    /**
     *
     * @return Company|null
     * @throws UnauthorizedApiException
     */
    public function checkApiAccess(): ?Company
    {
        $application = $this->getApplication();
        if (is_null($application)) {
            throw new UnauthorizedApiException();
        }
        if ($application->isThirdPartyCompanyApplication()) {
            $company = $this->getController()->company()->getCompany($application->getCompanyId());
            if (is_null($company)) {
                throw new UnauthorizedApiException('Access to company is forbidden');
            }
            return $company;
        }
        return null;
    }

    /**
     *
     * @return void
     * @throws UnauthorizedApiException
     */
    public function checkRobonoteApiOnlyAccess(): void
    {
        $application = $this->getApplication();
        if (!$application->isRobonoteApplication()) {
            throw new UnauthorizedApiException();
        }
    }

    /**
     *
     * @return void
     * @throws UnauthorizedApiException
     */
    public function checkRobonoteChildApiOnlyAccess(): void
    {
        $application = $this->getApplication();
        if (!$application->isChildApplication()) {
            throw new UnauthorizedApiException();
        }
    }

    /**
     *
     * @return Application
     * @throws UnauthorizedApiException
     */
    protected function getApplication(): Application
    {
        $authorizationHeader =
                $this->getController()->getRequest()->getHeaders()->has('authorization')
                ? $this->getController()->getRequest()->getHeaders()->get('authorization')->getFieldValue()
                : '';
        preg_match('/Bearer[\s]+(.*)/', $authorizationHeader, $matches);
        $token = isset($matches[1]) ? $matches[1] : null;
        if (is_null($token)) {
            throw new UnauthorizedApiException();
        }
        return $this->getController()->api()->application()->getApplicationByToken($token);
    }
}
