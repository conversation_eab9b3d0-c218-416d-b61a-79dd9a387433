<?php

declare(strict_types=1);

namespace STApi\Service;

trait RequestsLimitTrait
{
    use \STRedis\Service\ProvidesRedis;

    /**
     *
     * @var string
     */
    private string $requestsLimitRedisPrefix = 'requests-limit';

    /**
     *
     * @var RequestsLimitTraitWrapper|null
     */
    private ?RequestsLimitTraitWrapper $requestsLimitTraitWrapper = null;

    /**
     *
     * @param string $key
     * @param int $requestLimit
     * @param int $perSecond
     * @return bool
     * @throws \STApi\Entity\Exception\TooManyRequestsException
     */
    public function checkRequestLimit(string $key, int $requestLimit, int $perSecond): bool
    {
        $token = $this->getToken();
        if (empty($token)) {
            return true;
        }
        $requestId = microtime(true);
        $keyPath = $this->requestsLimitRedisPrefix . ':' . $token . ':' . $key . ':' . $perSecond;
        $count = count($this->redis()->keys($keyPath . ':' . '*'));
        if ($count >= $requestLimit) {
            throw new \STApi\Entity\Exception\TooManyRequestsException();
        }
        $this->redis()->set($keyPath . ':' . $requestId, \Carbon\Carbon::now());
        $this->redis()->expire($keyPath . ':' . $requestId, $perSecond);
        return true;
    }

    /**
     *
     * @return string|null
     */
    private function getToken(): ?string
    {
        if (is_null($this->requestsLimitTraitWrapper)) {
            $this->requestsLimitTraitWrapper = new RequestsLimitTraitWrapper();
        }
        $request = $this->requestsLimitTraitWrapper->getRequest();
        if (!($request instanceof \Laminas\Http\Request)) {
            return null;
        }
        return $request->getHeaders()->has('auth-token') ? $request->getHeaders()->get('auth-token')->getFieldValue() : null;
    }
}
