<?php

declare(strict_types=1);

namespace STApi;

class Module extends \STLib\ModuleManager\MultiConfigModule
{
    /**
     *
     * @param \Laminas\ModuleManager\ModuleManager $moduleManager
     * @return void
     */
    public function init(\Laminas\ModuleManager\ModuleManager $moduleManager): void
    {
        $events = $moduleManager->getEventManager();
        $events->attach(\Laminas\ModuleManager\ModuleEvent::EVENT_LOAD_MODULES_POST, function (\Laminas\ModuleManager\ModuleEvent $moduleEvent) {
            $serviceManager = $moduleEvent->getParam('ServiceManager');
            $request = $serviceManager->get('request');
            Service\RequestsLimitTraitWrapper::setRequest($request);
        });
    }
}
