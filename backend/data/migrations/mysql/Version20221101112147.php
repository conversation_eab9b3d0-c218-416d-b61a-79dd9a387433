<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221101112147 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('companies_algo_apis');
        $table->addColumn('algo_api_id', 'integer', [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('company_id', 'bigint', [
            'notnull' => true,
            'unsigned' => true,
        ]);
        
        $table->addForeignKeyConstraint('algo_apis', ['algo_api_id'], ['algo_api_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->addForeignKeyConstraint('companies', ['company_id'], ['company_id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ]);

        $table->setPrimaryKey([
            'algo_api_id',
            'company_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('companies_algo_apis');
    }

}
