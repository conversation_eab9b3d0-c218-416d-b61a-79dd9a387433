<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241224141735 extends AbstractMigration
{

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('onboarding_forms');

        $table->addColumn('onboarding_form_id', 'bigint', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->setPrimaryKey(['onboarding_form_id']);

        $table->addColumn('external_id', 'string', ['notnull' => true, 'length' => 255]);
        $table->addColumn('company_name', 'string', ['notnull' => false, 'length' => 255]);
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('onboarding_forms');
    }
}
