<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221101112146 extends AbstractMigration {

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->createTable('algo_apis');
        $table->addColumn('algo_api_id', 'integer', [
            'notnull' => true,
            'autoincrement' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('path', 'string', [
            'notnull' => true,
            'length' => 255,
        ]);

        $table->setPrimaryKey([
            'algo_api_id',
        ]);

        $table->addOption('charset', 'utf8mb4');
        $table->addOption('collation', 'utf8mb4_unicode_ci');
        $table->addOption('engine', 'InnoDB');
    }
    
    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function postUp(Schema $schema): void {
        $this->connection->executeQuery('
            INSERT INTO
                algo_apis
                (path)
            VALUES
                ("https://algo.robonote.io/api/v4"),
                ("https://algo.robonote.io/api/v5"),
                ("https://algo.robonote.io/api/v6"),
                ("https://algo.robonote.io/api/v8"),
                ("https://algo.robonote.io/api/v9"),
                ("https://algo16.robonote.io/api/v5"),
                ("https://algo16.robonote.io/api/v6"),
                ("https://algo16.robonote.io/api/v8"),
                ("https://algo.robonote.io/api/v9"),
                ("https://algo2.robonote.io/api/v3"),
                ("https://algo2.robonote.io/api/v4"),
                ("https://algo2.robonote.io/api/v5"),
                ("https://algo2.robonote.io/api/v6")
        ');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $schema->dropTable('algo_apis');
    }

}
