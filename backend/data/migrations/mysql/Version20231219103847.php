<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231219103847 extends AbstractMigration {

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->addColumn('is_s3_integration_enabled', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
    }

    /**
     *
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void
    {
        $table = $schema->getTable('companies');
        $table->dropColumn('is_s3_integration_enabled');
    }

}
