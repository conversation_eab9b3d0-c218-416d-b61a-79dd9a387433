<?php

declare(strict_types=1);

namespace Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220919145007 extends AbstractMigration
{

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function up(Schema $schema): void {
        $table = $schema->getTable('roles');
        $table->dropColumn('recordings_management_permission');
        $table->dropColumn('company_management_permission');
        $table->dropColumn('payment_management_permission');
        $table->dropColumn('iconery_management_permission');
    }

    /**
     * 
     * @param Schema $schema
     * @return void
     */
    public function down(Schema $schema): void {
        $table = $schema->getTable('roles');
        $table->addColumn('recordings_management_permission', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('company_management_permission', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('payment_management_permission', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
        $table->addColumn('iconery_management_permission', 'boolean', [
            'notnull' => true,
            'unsigned' => true,
            'default' => 0,
        ]);
    }
}
