<?php

namespace Clickhouse\Migrations;

class Version20250320101053 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_checklists_points
            ADD COLUMN
                client_id String
            AFTER call_type
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_checklists_points
            DROP COLUMN
                 client_id
        ');
    }
}
