<?php

namespace Clickhouse\Migrations;

class Version20241025120203 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                speaker_role Enum(\'agent\', \'client\', \'unclear\') DEFAULT \'unclear\'
            AFTER
                speaker_number
        ');

        $this->getClient()->write('
            ALTER TABLE
                calls
            ADD COLUMN
                is_speakers_roles_detected Bool DEFAULT 0
            AFTER
                is_translated
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                speaker_role
        ');

        $this->getClient()->write('
            ALTER TABLE
                calls
            DROP COLUMN
                is_speakers_roles_detected
        ');
    }
}
