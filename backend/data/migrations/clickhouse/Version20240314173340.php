<?php

namespace Clickhouse\Migrations;

class Version20240314173340 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                topic_id UInt32 DEFAULT 0
            AFTER
                timestamp
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                topic_name String
            AFTER
                topic_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                weight Int32 DEFAULT 0
            AFTER
                topic_name
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                topic_id
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                topic_name
        ');
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                weight
        ');
    }
}
