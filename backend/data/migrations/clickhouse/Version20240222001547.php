<?php

namespace Clickhouse\Migrations;

class Version20240222001547 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            CREATE TABLE precalculated_calls_events_tmp
            (
                `company_id` UInt32,
                `role_id` UInt32,
                `call_id` String,
                `call_time` DateTime DEFAULT now(),
                `agent_id` UInt128,
                `client_id` String,
                `call_origin` String DEFAULT \'api\',
                `paragraph` UInt32,
                `paragraph_start_time` UInt32 DEFAULT 0,
                `event_changed_from_event_id` Nullable(UInt64) DEFAULT NULL,
                `event_id` UInt64,
                `event_name` String,
                `event_score` Int32 DEFAULT 0,
                `event_highlight` Nullable(String),
                `event_en_highlight` Nullable(String),
                `event_text` Nullable(String),
                `event_en_text` Nullable(String),
                `event_icon` String,
                `event_is_pinned` Bo<PERSON>,
                `event_is_confirmed` Bool,
                `event_is_deleted` Bool DEFAULT 0,
                `event_category_id` UInt64,
                `event_category_name` String,
                `event_color_id` UInt64,
                `event_fill_color_hex` String,
                `event_outline_color_hex` String,
                `event_color_priority` UInt16,
                `created` DateTime64(3) DEFAULT now64()
            )
            ENGINE = ReplacingMergeTree(created)
            PARTITION BY company_id
            ORDER BY (company_id,
                role_id,
                call_id,
                paragraph,
                event_id)
            SETTINGS index_granularity = 8192;
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            DROP TABLE precalculated_calls_events_tmp
        ');
    }
}
