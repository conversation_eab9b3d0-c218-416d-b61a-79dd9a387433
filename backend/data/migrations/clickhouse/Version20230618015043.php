<?php

namespace Clickhouse\Migrations;

class Version20230618015043 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            ADD COLUMN
                event_score Int32 DEFAULT 0
            AFTER
                event_name
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                precalculated_calls_events
            DROP COLUMN
                event_score
        ');
    }
}
