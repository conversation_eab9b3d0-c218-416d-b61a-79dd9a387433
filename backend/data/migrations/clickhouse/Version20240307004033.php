<?php

namespace Clickhouse\Migrations;

class Version20240307004033 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            ADD COLUMN
                timestamp Nullable(DateTime) DEFAULT NULL
            AFTER
                end_time
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
            ALTER TABLE
                calls_paragraphs
            DROP COLUMN
                timestamp
        ');
    }
}
