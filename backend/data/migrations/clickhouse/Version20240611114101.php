<?php

namespace Clickhouse\Migrations;

class Version20240611114101 extends \STClickhouse\Entity\Migration\BaseMigration
{
    /**
     * 
     * @return void
     */
    public function up(): void
    {
        $this->getClient()->write('
             ALTER TABLE
                ems_data_sets
             ADD COLUMN
                is_deleted Bool DEFAULT 0
             AFTER
                status
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             ADD COLUMN
                is_deleted Bool DEFAULT 0
             AFTER
                status
        ');
    }

    /**
     * 
     * @return void
     */
    public function down(): void
    {
        $this->getClient()->write('
             ALTER TABLE
                ems_data_sets
             DROP COLUMN
                is_deleted
        ');
        $this->getClient()->write('
             ALTER TABLE
                ems_data_set_examples
             DROP COLUMN
                is_deleted
        ');
    }
}
